﻿using RoofSnap.Core.Models;
using RoofSnap.Events.Core;
using RoofSnap.WebAPI.Areas.Projects;
using RoofSnap.WebAPI.Areas.SketchOrders.Data;
using RoofSnap.WebAPI.Common.Data;

namespace RoofSnap.WebAPI.Common.Events
{
    public interface IRoofSnapApiEventPublisher
    {
        void PublishSketchOrderCompletedEvent(DbSketchOrder sketchOrder);
        void PublishSketchOrderNotCompletedEvent(DbSketchOrder sketchOrder);
        void PublishUserProfileActivationToggledEvent(UserProfileModel userProfile);
        void PublishProjectStatusChangedEvent(ProjectModel project);
        void PublishProjectUpdatedEvent(ProjectModel project);
        void PublishOrganizationCreatedEvent(long organizationId, long officeId);
        void PublishSketchOrderCreatedEvent(DbSketchOrder sketchOrder);
        void PublishSketchOrderUpdatedEvent(DbSketchOrder sketchOrder);
        void PublishSketchOrderUpdatedEvent(DbProject project);
    }

    public class RoofSnapApiEventPublisher : IRoofSnapApiEventPublisher
    {
        private readonly IRoofSnapEventPublisher _roofSnapEventPublisher;
        private readonly IUnitOfWork _unitOfWork;

        public RoofSnapApiEventPublisher(IRoofSnapEventPublisher roofSnapEventPublisher,
            IUnitOfWork unitOfWork)
        {
            _roofSnapEventPublisher = roofSnapEventPublisher;
            _unitOfWork = unitOfWork;
        }

        private void PublishEventOnPostCommit(string subject,
            RoofSnapEventType eventType,
            long? officeId,
            long? organizationId,
            string resourceId)
        {
            _unitOfWork.AddPostCommitAction(() =>
                _roofSnapEventPublisher.PublishRoofSnapEventAsync(new RoofSnapEvent
                {
                    Subject = subject,
                    EventType = eventType,
                    EventData = new RoofSnapEventData
                    {
                        OfficeId = officeId,
                        OrganizationId = organizationId,
                        ResourceId = resourceId,
                        Platform = RoofSnapPlatform.Determine()
                    }
                }));
        }

        public void PublishSketchOrderCompletedEvent(DbSketchOrder sketchOrder)
        {
            PublishEventOnPostCommit($"/sketchorders/{sketchOrder.Id}",
                RoofSnapEventType.SketchOrderCompleted,
                sketchOrder.Project.OfficeId, // WARNING: Make sure nav property is loaded if this gets converted to EFCore
                sketchOrder.Organizationid,
                sketchOrder.Id.ToString());
        }

        public void PublishSketchOrderNotCompletedEvent(DbSketchOrder sketchOrder)
        {
            PublishEventOnPostCommit($"/sketchorders/{sketchOrder.Id}",
                RoofSnapEventType.SketchOrderNotCompleted,
                sketchOrder.Project?.OfficeId, // WARNING: Make sure nav property is loaded if this gets converted to EFCore
                sketchOrder.Organizationid,
                sketchOrder.Id.ToString());
        }

        public void PublishUserProfileActivationToggledEvent(UserProfileModel userProfile)
        {
            PublishEventOnPostCommit($"/userprofiles/{userProfile.UserId}",
                userProfile.Active ? RoofSnapEventType.UserProfileActivated : RoofSnapEventType.UserProfileDeactivated,
                null,
                userProfile.OrganizationId,
                userProfile.UserId.ToString());
        }

        public void PublishProjectStatusChangedEvent(ProjectModel project)
        {
            PublishEventOnPostCommit($"/projects/{project.Id}",
                RoofSnapEventType.ProjectStatusChanged,
                project.OfficeId,
                project.OrganizationId,
                project.Id);
        }

        // This is normally handled through the CRUD event handlers tied directly to the dbcontext, but needs exposed separately for restoring deleted projects
        public void PublishProjectUpdatedEvent(ProjectModel project)
        {
            PublishEventOnPostCommit($"/projects/{project.Id}",
                RoofSnapEventType.ProjectUpdated,
                project.OfficeId,
                project.OrganizationId,
                project.Id);
        }
        
        // Not handled in CRUD handler for orgs created with a stored procedure
        public void PublishOrganizationCreatedEvent(long organizationId, long officeId)
        {
            PublishEventOnPostCommit($"/organizations/{organizationId}",
                RoofSnapEventType.OrganizationCreated,
                officeId,
                organizationId,
                organizationId.ToString());
        }

        public void PublishSketchOrderCreatedEvent(DbSketchOrder sketchOrder)
        {
            PublishEventOnPostCommit($"/sketchorders/{sketchOrder.Id}",
                RoofSnapEventType.SketchOrderCreated,
                sketchOrder.DeliveryOfficeId,
                sketchOrder.Organizationid,
                sketchOrder.Id.ToString());
        }

        public void PublishSketchOrderUpdatedEvent(DbSketchOrder sketchOrder) =>
            PublishEventOnPostCommit($"/sketchorders/{sketchOrder.Id}",
                                     RoofSnapEventType.SketchUpdated,
                                     sketchOrder.DeliveryOfficeId,
                                     sketchOrder.Organizationid,
                                     sketchOrder.Id.ToString());

        public void PublishSketchOrderUpdatedEvent(DbProject project) =>
            PublishEventOnPostCommit($"/projects/{project.Id}",
                                     RoofSnapEventType.SketchUpdated,
                                     project.OfficeId,
                                     project.OrganizationId,
                                     project.Id);
    }
}