﻿using System.Collections.Generic;
using Newtonsoft.Json;

namespace RoofSnap.WebAPI.Areas.Documents
{
    public class CreateDocumentV2Dto
    {
        public string DataContextEntityId { get; set; }
        public string[] EstimateOptionIds { get; set; }

        public long OrganizationId { get; set; }

        public bool IsESignContract { get; set; }

        public bool IsFeaturedContract { get; set; }

        public Dictionary<string, string> HtmlFormData { get; set; }

        [JsonProperty(PropertyName = "TemplateId")]
        public string TemplateShortCode { get; set; }

        public string Name { get; set; }
    }
}