﻿using Microsoft.EntityFrameworkCore;
using RoofSnap.Core.Data;
using RoofSnap.Core.Models;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace RoofSnap.WebAPI.Areas.Organizations.OrganizationFeatures
{
    public class EnableSmartEstimatesOrganizationFeatureStrategy : IOrganizationFeatureStrategy
    {
        private readonly IRoofSnapData _roofSnapData;

        public EnableSmartEstimatesOrganizationFeatureStrategy(IRoofSnapData roofSnapData)
        {
            _roofSnapData = roofSnapData;
        }

        public async Task<OrganizationFeature> EvaluateAsync(OrganizationModel organization)
        {
            IQueryable<OrganizationSettingsModel> query = _roofSnapData.Entry(organization)
                .Collection(x => x.OrganizationSettings)
                .Query();

            bool enabled = await query.AnyAsync(x =>
                x.SettingType == OrganizationSettingTypesModel.EnableSmartEstimates);

            return new OrganizationFeature
            {
                Name = OrganizationFeatureType.EnableSmartEstimates,
                Enabled = enabled,
                OrganizationId = organization.Id
            };
        }

        public Task<OrganizationFeature> EnableAsync(long organizationId)
        {
            throw new NotImplementedException();
        }

        public Task<OrganizationFeature> DisableAsync(long organizationId)
        {
            throw new NotImplementedException();
        }
    }
}