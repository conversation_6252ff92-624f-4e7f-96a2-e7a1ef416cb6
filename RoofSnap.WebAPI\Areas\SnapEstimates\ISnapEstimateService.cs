﻿using RoofSnap.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RoofSnap.WebAPI.Areas.SnapEstimates
{
    public interface ISnapEstimateService
    {
        Task<ProjectSnapEstimateModel> CreateSnapEstimate(CreateSnapEstimateRequestDto dto);
        Task<ProjectSnapEstimateModel> UpdateSnapEstimateAsync(string id, UpdateSnapEstimateDto dto);
        Task<LineItemDto> UpdateLineItemAsync(string id, int lineItemId, LineItemDto lineItemDto);
        Task UpdateNameAsync(string id, string name);
        Task<IList<SnapEstimateRepresentation>> GetSnapEstimatesByProjectIdAsync(string projectId);
        Task<ProjectSnapEstimateModel> DeleteSnapEstimateAsync(string id);
        Task<SnapEstimateRepresentation> GetAsync(string id);
        Task<IList<string>> GetUniqueRoofMaterialsAsync();
    }
}