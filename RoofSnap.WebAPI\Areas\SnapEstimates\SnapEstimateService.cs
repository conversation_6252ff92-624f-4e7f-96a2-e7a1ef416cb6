﻿using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RoofSnap.Core.Data;
using RoofSnap.Core.Models;
using RoofSnap.WebAPI.Areas.AI;
using RoofSnap.WebAPI.Common.Config;
using RoofSnap.WebAPI.Common.Data;
using RoofSnap.WebAPI.Common.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RoofSnap.WebAPI.Areas.SnapEstimates
{
    public class SnapEstimateService : ISnapEstimateService
    {
        private readonly IAiService _aiService;
        private readonly IErrorLogger _logger;
        private readonly IConfig _config;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IRoofSnapData _roofSnapData;

        public SnapEstimateService(IAiService aiService,
            IErrorLogger logger,
            IConfig config,
            IUnitOfWork unitOfWork,
            IRoofSnapData roofSnapData)
        {
            _aiService = aiService;
            _logger = logger;
            _config = config;
            _unitOfWork = unitOfWork;
            _roofSnapData = roofSnapData;
        }

        public async Task<ProjectSnapEstimateModel> CreateSnapEstimate(CreateSnapEstimateRequestDto dto)
        {

            var questionPrompt = new QuestionPrompt(_aiService);
            var prompt = ConstructPrompt(dto.CustomerAddress, dto.RoofMaterialName);

            string lineItems = await FetchLineItemsFromAIorDB(dto.RoofMaterialName);

            var snapEstimate = new ProjectSnapEstimateModel
            {
                ProjectId = dto.ProjectId,
                Name = dto.RoofMaterialName,
                RoofMaterialName = dto.RoofMaterialName,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now,
                LineItems = lineItems,
                RoofArea = dto.RoofArea,
                MaterialCostPerSquare = dto.MaterialCostPerSquare,
                LaborCostPerSquare = dto.LaborCostPerSquare,
                ProfitMarginAndMarkup = dto.ProfitMarginAndMarkup,
                PriceRange = dto.PriceRange,
                TotalCostPerSquare = dto.TotalCostPerSquare,
                Description = dto.Description,
            };

            await _unitOfWork.RunAsync(async () =>
            {
                _roofSnapData.ProjectSnapEstimates.Add(snapEstimate);
                await _roofSnapData.SaveChangesAsync();
            });

            return snapEstimate;
        }

        public async Task<IList<SnapEstimateRepresentation>> GetSnapEstimatesByProjectIdAsync(string projectId)
        {
            try
            {
                var snapEstimates = await _roofSnapData.ProjectSnapEstimates
                                   .Where(e => e.ProjectId == projectId)
                                   .ToListAsync();

                var result = snapEstimates.Select(e => new SnapEstimateRepresentation
                {
                    Id = e.Id,
                    ProjectId = e.ProjectId,
                    Name = e.Name,
                    RoofMaterialName = e.RoofMaterialName,
                    CreatedAt = e.CreatedAt,
                    UpdatedAt = e.UpdatedAt,
                    LineItems = e.LineItems,
                    RoofArea = e.RoofArea ?? 0,
                    MaterialCostPerSquare = e.MaterialCostPerSquare ?? 0,
                    LaborCostPerSquare = e.LaborCostPerSquare ?? 0,
                    ProfitMarginAndMarkup = e.ProfitMarginAndMarkup ?? 0,
                    PriceRange = e.PriceRange ?? 0,
                    TotalCostPerSquare = e.TotalCostPerSquare ?? 0,
                    Description = e.Description,
                    EstimateType = EstimateType.Snap
                }).ToList();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                return null;
            }
        }

        public async Task<ProjectSnapEstimateModel> UpdateSnapEstimateAsync(string id, UpdateSnapEstimateDto dto)
        {
            var snapEstimate = _roofSnapData.ProjectSnapEstimates.FirstOrDefault(e => e.Id == id);

            if (snapEstimate == null)
            {
                return null;
            }

            if (!string.IsNullOrEmpty(dto.LineItems))
            {
                snapEstimate.LineItems = dto.LineItems;
            }
            else
            {
                snapEstimate.LineItems = await FetchLineItemsFromAIorDB(dto.RoofMaterialName);
            }

            snapEstimate.Name = dto.Name;
            snapEstimate.RoofMaterialName = dto.RoofMaterialName;
            snapEstimate.UpdatedAt = DateTime.Now;
            snapEstimate.RoofArea = dto.RoofArea;
            snapEstimate.MaterialCostPerSquare = dto.MaterialCostPerSquare;
            snapEstimate.LaborCostPerSquare = dto.LaborCostPerSquare;
            snapEstimate.ProfitMarginAndMarkup = dto.ProfitMarginAndMarkup;
            snapEstimate.PriceRange = dto.PriceRange;
            snapEstimate.TotalCostPerSquare = dto.TotalCostPerSquare;
            snapEstimate.TaxPercentage = dto.TaxPercentage;
            snapEstimate.DiscountPercentage = dto.DiscountPercentage;
            snapEstimate.DiscountDescription = dto.DiscountDescription;
            snapEstimate.Description = dto.Description;

            _roofSnapData.ProjectSnapEstimates.Update(snapEstimate);

            return snapEstimate;
        }

        public Task<LineItemDto> UpdateLineItemAsync(string id, int lineItemId, LineItemDto lineItemDto)
        {
            var snapEstimate = _roofSnapData.ProjectSnapEstimates.FirstOrDefault(e => e.Id == id);

            if (snapEstimate == null)
            {
                return Task.FromResult<LineItemDto>(null);
            }

            try
            {
                // Parse the existing line items JSON
                JArray lineItemsArray;
                if (string.IsNullOrEmpty(snapEstimate.LineItems))
                {
                    lineItemsArray = new JArray();
                }
                else
                {
                    lineItemsArray = JArray.Parse(snapEstimate.LineItems);
                }

                // Find the line item to update
                var lineItemToUpdate = lineItemsArray.FirstOrDefault(item =>
                {
                   return Convert.ToInt32(item["id"] ?? item["Id"]) == lineItemId;
                });

                if (lineItemToUpdate == null)
                {
                    // Line item not found
                    return Task.FromResult<LineItemDto>(null);
                }

                // Update the line item properties
                lineItemToUpdate["Name"] = lineItemDto.Name;
                lineItemToUpdate["Description"] = lineItemDto.Description;

                // Update the snap estimate
                snapEstimate.LineItems = lineItemsArray.ToString(Formatting.None);
                snapEstimate.UpdatedAt = DateTime.UtcNow;

                _roofSnapData.ProjectSnapEstimates.Update(snapEstimate);

                // Return the updated line item
                var updatedLineItem = new LineItemDto
                {
                    Id = lineItemId.ToString(),
                    Name = lineItemDto.Name,
                    Description = lineItemDto.Description
                };

                return Task.FromResult(updatedLineItem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                return Task.FromResult<LineItemDto>(null);
            }
        }

        public async Task UpdateNameAsync(string id, string name)
        {
            var snapEstimate = await _roofSnapData.ProjectSnapEstimates.FirstOrDefaultAsync(e => e.Id == id);
            snapEstimate.Name = name;
            _roofSnapData.ProjectSnapEstimates.Update(snapEstimate);
        }

        public Task<ProjectSnapEstimateModel> DeleteSnapEstimateAsync(string id)
        {
            var snapEstimate = _roofSnapData.ProjectSnapEstimates.FirstOrDefault(e => e.Id == id);

            if (snapEstimate == null)
            {
                return Task.FromResult<ProjectSnapEstimateModel>(null);
            }

            _roofSnapData.ProjectSnapEstimates.Remove(snapEstimate);
            return Task.FromResult(snapEstimate);
        }

        public async Task<SnapEstimateRepresentation> GetAsync(string id)
        {
            try
            {
                var snapEstimate = await _roofSnapData.ProjectSnapEstimates
                                   .Where(e => e.Id == id)
                                   .ToListAsync();

                var result = snapEstimate.Select(e => new SnapEstimateRepresentation
                {
                    Id = e.Id,
                    ProjectId = e.ProjectId,
                    Name = e.Name,
                    RoofMaterialName = e.RoofMaterialName,
                    CreatedAt = e.CreatedAt,
                    UpdatedAt = e.UpdatedAt,
                    LineItems = e.LineItems,
                    RoofArea = e.RoofArea ?? 0,
                    MaterialCostPerSquare = e.MaterialCostPerSquare ?? 0,
                    LaborCostPerSquare = e.LaborCostPerSquare ?? 0,
                    ProfitMarginAndMarkup = e.ProfitMarginAndMarkup ?? 0,
                    PriceRange = e.PriceRange ?? 0,
                    TotalCostPerSquare = e.TotalCostPerSquare ?? 0,
                    TaxPercentage = e.TaxPercentage ?? 0,
                    DiscountPercentage = e.DiscountPercentage ?? 0,
                    DiscountDescription = e.DiscountDescription,
                    Description = e.Description,
                    IsChosen = e.IsChosen,
                    EstimateType = EstimateType.Snap
                })
                             .FirstOrDefault();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                return null;
            }
        }

        private string ConstructPrompt(string address, string materialName)
        {
            var prompt = _config.SnapEstimateAiPrompt;
            prompt = prompt.Replace("[address]", address);
            prompt = prompt.Replace("[material]", materialName);
            return prompt;
        }

        private async Task<string> GenerateLineItems(string address, string materialName)
        {
            var questionPrompt = new QuestionPrompt(_aiService);
            var prompt = ConstructPrompt(address, materialName);
            return await questionPrompt.AskQuestion(prompt);
        }

        private async Task<string> FetchLineItemsFromAIorDB(string roofMaterialName)
        {
            try
            {
                //return await questionPrompt.AskQuestion(prompt);
            }
            catch (Exception ex)
            {
                _logger.LogError(new Exception("Error connecting AI service", ex));
            }

            var snapEstimatesLineItems = await GetLineItemsMasterFromDB(roofMaterialName);

            if (snapEstimatesLineItems.Count == 0)
            {
                snapEstimatesLineItems = await GetLineItemsMasterFromDB("Generic / Custom fallback");
            }

            return JsonConvert.SerializeObject(snapEstimatesLineItems);
        }

        private async Task<List<object>> GetLineItemsMasterFromDB(string roofMaterialName)
        {
            return await _roofSnapData.ProjectSnapEstimatesLineItemsMaster
                .Where(item => item.RoofMaterialName == roofMaterialName)
                .Select(item => new { 
                    item.Id, 
                    item.Name, 
                    item.Description 
                })
                .Cast<object>()
                .ToListAsync();
        }

        public async Task<IList<string>> GetUniqueRoofMaterialsAsync()
        {
            var uniqueRoofMaterials = await _roofSnapData.ProjectSnapEstimatesLineItemsMaster
                                        .Where(item => item.RoofMaterialName != "Generic / Custom fallback")
                                        .Select(item => item.RoofMaterialName)
                                        .Distinct()
                                        .ToListAsync();
            return uniqueRoofMaterials;
        }
    }
}