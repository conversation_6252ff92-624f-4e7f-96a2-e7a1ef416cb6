﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RoofSnap.Core.Models;

namespace RoofSnap.Core.Data.Configurations
{
    public class ProjectSnapEstimatesLineItemsMasterConfiguration : IEntityTypeConfiguration<ProjectSnapEstimatesLineItemsMasterModel>
    {
        public void Configure(EntityTypeBuilder<ProjectSnapEstimatesLineItemsMasterModel> builder)
        {
            builder.ToTable("ProjectSnapEstimatesLineItemsMaster", "roofsnaplive");

            builder.Property(x => x.Id)
                .HasColumnName("id")
                .HasColumnType(SqlTypes.Int)
                .IsRequired();

            builder.Property(r => r.RoofMaterialName)
                .HasColumnName("roofMaterialName")
                .HasColumnType(SqlTypes.Nvarchar100)
                .IsRequired();
            
            builder.Property(r => r.Name)
                .HasColumnName("name")
                .HasColumnType(SqlTypes.Nvarchar100)
                .IsRequired();

            builder.Property(r => r.Description)
                .HasColumnName("description")
                .HasColumnType(SqlTypes.NvarcharMax)
                .IsRequired();

            builder.Property(rn => rn.CreatedAt)
                .HasColumnName("__createdAt")
                .HasColumnType(SqlTypes.DateTimeOffset3)
                .IsRequired()
                .HasDefaultValueSql("CONVERT([datetimeoffset](3),sysutcdatetime(),(0))");

            builder.Property(rn => rn.UpdatedAt)
                .HasColumnName("__updatedAt")
                .HasColumnType(SqlTypes.DateTimeOffset3)
                .HasDefaultValueSql("Sysdatetimeoffset()");
        }
    }
}
