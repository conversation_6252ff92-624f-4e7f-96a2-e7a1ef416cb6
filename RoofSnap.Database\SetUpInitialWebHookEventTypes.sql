﻿if (NOT EXISTS (SELECT TOP 1 1 FROM [roofsnaplive].[WebHookEventTypes] WHERE Id = 1))
BEGIN
	INSERT INTO [roofsnaplive].[WebHookEventTypes] (Id, EventType) VALUES (1, 'ProjectCreated')
END

if (NOT EXISTS (SELECT TOP 1 1 FROM [roofsnaplive].[WebHookEventTypes] WHERE Id = 2))
BEGIN
	INSERT INTO [roofsnaplive].[WebHookEventTypes] (Id, EventType) VALUES (2, 'SketchOrderCompleted')
END

if (NOT EXISTS (SELECT TOP 1 1 FROM [roofsnaplive].[WebHookEventTypes] WHERE Id = 3))
BEGIN
	INSERT INTO [roofsnaplive].[WebHookEventTypes] (Id, EventType) VALUES (3, 'SketchOrderNotCompleted')
END

if (NOT EXISTS (SELECT TOP 1 1 FROM [roofsnaplive].[WebHookEventTypes] WHERE Id = 4))
BEGIN
	INSERT INTO [roofsnaplive].[WebHookEventTypes] (Id, EventType) VALUES (4, 'ProjectStatusChanged')
END

if (NOT EXISTS (SELECT TOP 1 1 FROM [roofsnaplive].[WebHookEventTypes] WHERE Id = 14))
BEGIN
INSERT INTO [roofsnaplive].[WebHookEventTypes] (Id, EventType) VALUES (14, 'SketchUpdated')
END