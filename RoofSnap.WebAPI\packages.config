﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="ActionMailerNext" version="3.2.0.0" targetFramework="net472" />
  <package id="ActionMailerNext.Mvc5-2" version="3.2.0.0" targetFramework="net472" />
  <package id="AutoMapper" version="5.2.0" targetFramework="net472" />
  <package id="Braintree" version="4.8.0" targetFramework="net472" />
  <package id="Castle.Core" version="4.2.0" targetFramework="net472" />
  <package id="CsQuery" version="1.3.4" targetFramework="net472" />
  <package id="DistributedLock" version="1.4.0" targetFramework="net472" />
  <package id="EntityFramework" version="6.2.0" targetFramework="net472" />
  <package id="FastMember" version="1.0.0.11" targetFramework="net472" />
  <package id="FileHelpers" version="3.2.7" targetFramework="net472" />
  <package id="Flurl" version="2.5.0" targetFramework="net472" />
  <package id="Flurl.Http" version="2.0.1" targetFramework="net472" />
  <package id="GeoAPI.CoordinateSystems" version="1.7.5" targetFramework="net472" />
  <package id="GeoAPI.Core" version="1.7.5" targetFramework="net472" />
  <package id="GeoJSON.Net" version="0.1.51" targetFramework="net472" />
  <package id="GeoJSON.Net.Contrib.MsSqlSpatial" version="0.2.0" targetFramework="net472" />
  <package id="Google.Apis.Core" version="1.32.2" targetFramework="net472" />
  <package id="Handlebars.Net" version="1.9.0" targetFramework="net472" />
  <package id="HtmlAgilityPack" version="1.4.9.5" targetFramework="net472" />
  <package id="IdentityModel" version="2.0.1" targetFramework="net472" />
  <package id="iTextSharp" version="5.5.11" targetFramework="net472" />
  <package id="itextsharp.xmlworker" version="5.5.11" targetFramework="net472" />
  <package id="LINQtoCSV" version="1.5.0.0" targetFramework="net472" />
  <package id="Logzio.DotNet.NLog" version="1.0.6" targetFramework="net472" />
  <package id="Mandrill" version="1.2.1.0" targetFramework="net472" />
  <package id="Marvin.JsonPatch" version="1.0.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights" version="2.10.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.Agent.Intercept" version="2.4.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.DependencyCollector" version="2.10.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.NLogTarget" version="2.10.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.PerfCounterCollector" version="2.10.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.Web" version="2.10.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.WindowsServer" version="2.10.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" version="2.10.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.Cors" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Razor" version="3.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.TelemetryCorrelation" version="1.0.5" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Cors" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages.Data" version="3.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages.WebData" version="3.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Authentication.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Authentication.Core" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Authorization" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Authorization.Policy" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Hosting.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Hosting.Server.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Extensions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Features" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Core" version="2.2.5" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.ResponseCaching.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Routing" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Routing.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.WebUtilities" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Azure.EventGrid" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Azure.KeyVault.Core" version="1.0.0" targetFramework="net472" />
  <package id="Microsoft.Azure.Search" version="10.1.0" targetFramework="net472" />
  <package id="Microsoft.Azure.Search.Common" version="10.1.0" targetFramework="net472" />
  <package id="Microsoft.Azure.Search.Data" version="10.1.0" targetFramework="net472" />
  <package id="Microsoft.Azure.Search.Service" version="10.1.0" targetFramework="net472" />
  <package id="Microsoft.Azure.Services.AppAuthentication" version="1.3.1" targetFramework="net472" />
  <package id="Microsoft.Azure.Storage.Common" version="10.0.3" targetFramework="net472" />
  <package id="Microsoft.Azure.Storage.DataMovement" version="0.7.1" targetFramework="net472" />
  <package id="Microsoft.Azure.Storage.Queue" version="10.0.3" targetFramework="net472" />
  <package id="Microsoft.Bcl" version="1.1.10" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.Build" version="1.0.21" targetFramework="net472" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net472" />
  <package id="Microsoft.Data.Edm" version="5.7.0" targetFramework="net472" />
  <package id="Microsoft.Data.OData" version="5.7.0" targetFramework="net472" />
  <package id="Microsoft.Data.Services.Client" version="5.7.0" targetFramework="net472" />
  <package id="Microsoft.DotNet.PlatformAbstractions" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.Abstractions" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.Analyzers" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.Relational" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.SqlServer" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.Extensions.Caching.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Caching.Memory" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyModel" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Debug" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.ObjectPool" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Clients.ActiveDirectory" version="4.3.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Logging" version="1.0.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Tokens" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Net.Compilers" version="3.3.1" targetFramework="net472" developmentDependency="true" />
  <package id="Microsoft.Net.Http" version="2.2.29" targetFramework="net472" />
  <package id="Microsoft.Net.Http.Headers" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Rest.ClientRuntime" version="2.3.20" targetFramework="net472" />
  <package id="Microsoft.Rest.ClientRuntime.Azure" version="3.3.19" targetFramework="net472" />
  <package id="Microsoft.Spatial" version="7.5.3" targetFramework="net472" />
  <package id="Microsoft.SqlServer.Types" version="14.0.1016.290" targetFramework="net472" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net472" />
  <package id="Microsoft.WindowsAzure.ConfigurationManager" version="3.2.3" targetFramework="net472" />
  <package id="Mindscape.Raygun4Net.Core" version="5.7.0" targetFramework="net472" />
  <package id="Mindscape.Raygun4Net.WebApi" version="5.7.0" targetFramework="net472" />
  <package id="NetTopologySuite" version="1.15.3" targetFramework="net472" />
  <package id="NetTopologySuite.CoordinateSystems" version="1.15.3" targetFramework="net472" />
  <package id="NetTopologySuite.Core" version="1.15.3" targetFramework="net472" />
  <package id="NetTopologySuite.IO.SqlServerBytes" version="1.15.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net472" />
  <package id="Ninject" version="3.3.4" targetFramework="net472" />
  <package id="Ninject.Extensions.Conventions" version="3.3.0" targetFramework="net472" />
  <package id="Ninject.Extensions.Factory" version="3.3.2" targetFramework="net472" />
  <package id="Ninject.Web.Common" version="3.3.0" targetFramework="net472" />
  <package id="Ninject.Web.Common.WebHost" version="3.3.0" targetFramework="net472" />
  <package id="Ninject.Web.WebApi" version="3.3.0" targetFramework="net472" />
  <package id="Ninject.Web.WebApi.WebHost" version="3.3.0" targetFramework="net472" />
  <package id="NLog" version="4.7.2" targetFramework="net472" />
  <package id="NLog.Config" version="4.7.2" targetFramework="net472" />
  <package id="NLog.Schema" version="4.7.2" targetFramework="net472" />
  <package id="NLog.Web" version="4.9.2" targetFramework="net472" />
  <package id="Polly" version="7.1.0" targetFramework="net472" />
  <package id="PreMailer.Net" version="1.3.0" targetFramework="net472" />
  <package id="RazorEngine" version="3.9.0" targetFramework="net472" />
  <package id="Remotion.Linq" version="2.2.0" targetFramework="net472" />
  <package id="RestSharp" version="105.2.3" targetFramework="net472" />
  <package id="RoofSnap.Events.Core" version="1.0.314" targetFramework="net472" />
  <package id="RoofSnap.Functions.Models" version="1.0.18" targetFramework="net472" />
  <package id="RoofSnap.GeoCode.Client" version="1.0.15" targetFramework="net472" />
  <package id="RoofSnap.GeoCode.Model" version="1.0.15" targetFramework="net472" />
  <package id="RoofSnap.GraphingUtilities" version="1.0.7" targetFramework="net472" />
  <package id="RoofSnap.Measurements" version="1.1.12" targetFramework="net472" />
  <package id="RoofSnap.Search.Models" version="1.0.16" targetFramework="net472" />
  <package id="SalesforceMagic" version="*******" targetFramework="net472" />
  <package id="Sendgrid" version="9.9.0" targetFramework="net472" />
  <package id="SendGrid.SmtpApi" version="1.3.3" targetFramework="net472" />
  <package id="StackifyHttpModule" version="1.0.41" targetFramework="net472" />
  <package id="Stripe.net" version="47.3.0-beta.1" targetFramework="net472" />
  <package id="Swashbuckle" version="5.6.0" targetFramework="net472" />
  <package id="Swashbuckle.Core" version="5.6.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.0" targetFramework="net472" />
  <package id="System.Collections" version="4.0.11" targetFramework="net472" />
  <package id="System.Collections.Immutable" version="1.5.0" targetFramework="net472" />
  <package id="System.ComponentModel.Annotations" version="5.0.0" targetFramework="net472" />
  <package id="System.Data.DataSetExtensions" version="4.5.0" targetFramework="net472" />
  <package id="System.Data.SqlClient" version="4.6.0" targetFramework="net472" />
  <package id="System.Diagnostics.Debug" version="4.0.11" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.5.0" targetFramework="net472" />
  <package id="System.IdentityModel.Tokens.Jwt" version="5.0.0" targetFramework="net472" />
  <package id="System.Interactive.Async" version="3.2.0" targetFramework="net472" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.Linq" version="4.1.0" targetFramework="net472" />
  <package id="System.Linq.Expressions" version="4.1.0" targetFramework="net472" />
  <package id="System.Linq.Queryable" version="4.0.1" targetFramework="net472" />
  <package id="System.Memory" version="4.5.1" targetFramework="net472" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.4.0" targetFramework="net472" />
  <package id="System.ObjectModel" version="4.0.12" targetFramework="net472" />
  <package id="System.Reflection" version="4.1.0" targetFramework="net472" />
  <package id="System.Reflection.Extensions" version="4.0.1" targetFramework="net472" />
  <package id="System.Runtime" version="4.3.1" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.2" targetFramework="net472" />
  <package id="System.Runtime.Extensions" version="4.1.0" targetFramework="net472" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net472" />
  <package id="System.Spatial" version="5.7.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="4.5.0" targetFramework="net472" />
  <package id="System.Threading" version="4.0.11" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.2" targetFramework="net472" />
  <package id="WebActivatorEx" version="2.1.0" targetFramework="net472" />
  <package id="WebApi.Hal" version="2.6.0" targetFramework="net472" />
  <package id="WindowsAzure.Storage" version="9.0.0" targetFramework="net472" />
  <package id="XkPassword" version="1.1.2.0" targetFramework="net472" />
</packages>