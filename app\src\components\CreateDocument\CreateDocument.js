import React, { Component, createRef } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { getDocuments } from '../../actions/documentsActions';
import { getProjectById } from '../../actions/ProjectActions';
import { getDocumentTemplateCategories } from '../../actions/documentTemplateCategoriesActions';
import FloatingActionButton from '../FloatingActionButton';
import CreateDocumentDialog from '../CreateDocumentDialog';
import DocumentCreator from './DocumentCreator';
import { DocumentDataContextType } from '../../lib/documentDataContextType';

class CreateDocument extends Component {
    state = {
        newCreateDocumentDialogIsOpen: this.props.open,
        template: null,
    };

    async componentDidMount() {
        await this.props.dispatch(getProjectById(this.props.projectId));
        await this.props.dispatch(
            getDocumentTemplateCategories(this.props.officeId)
        );
    }

    setTemplate = (template) => {
        this.setState({
            template,
        });
    };

    resetState = () => {
        this.setState({
            newCreateDocumentDialogIsOpen: false,
        });
    };

    documentCreatorRef = createRef();

    openNewDocumentDialog = () => {
        this.setState({
            newCreateDocumentDialogIsOpen: true,
        });
    };

    handleStartCreateDocument = async (
        dataContextEntityId,
        estimateOptionIds
    ) => {
        const templateIds = [this.state.template.id];
        const hasV2HtmlForm =
            !this.state.template.usesLegacySigningWorkflow &&
            this.state.template.hasHtmlForm;
        await this.documentCreatorRef.current?.startCreateDocument(
            templateIds,
            hasV2HtmlForm,
            dataContextEntityId,
            estimateOptionIds,
            null,
            false
        );

        await this.props.dispatch(getDocuments(this.props.projectId));
        this.resetState();
    };

    render() {
        const disabledDocumentDataContextTypes = [];
        const { subscriptionExpired } = this.props.organization;

        if (!this.props.projectHasDrawing) {
            disabledDocumentDataContextTypes.push(
                DocumentDataContextType.ProjectDrawing
            );
        }

        if (
            this.props.estimates.length === 0 ||
            this.props.organization.subscriptionExpired
        ) {
            disabledDocumentDataContextTypes.push(
                DocumentDataContextType.Estimate,
                DocumentDataContextType.MultiEstimate
            );
        }

        return (
            <div>
                <CreateDocumentDialog
                    documentTemplateCategories={
                        this.props.documentTemplateCategories
                    }
                    onDocumentTemplateClick={async (template, category) => {
                        this.setState({
                            template,
                        });
                        await this.documentCreatorRef.current?.handleSelectDocumentTemplate(
                            template,
                            category.name
                        );
                    }}
                    open={this.state.newCreateDocumentDialogIsOpen}
                    onDismissClick={this.resetState}
                    disabledDocumentDataContextTypes={
                        disabledDocumentDataContextTypes
                    }
                    currentUserRoleIds={this.props.currentUserRoleIds}
                    officeId={this.props.officeId}
                    subscriptionExpired={subscriptionExpired}
                />

                <DocumentCreator
                    ref={this.documentCreatorRef}
                    dispatch={this.props.dispatch}
                    organizationId={this.props.organization.id}
                    projectId={this.props.projectId}
                    estimates={this.props.estimates}
                    onEstimateDismiss={this.openNewDocumentDialog}
                    onSetTemplate={this.setTemplate}
                    onStartCreateDocument={this.handleStartCreateDocument}
                />

                <FloatingActionButton
                    onClick={this.openNewDocumentDialog}
                    variant='extended'
                    id='addNewButton'
                />
            </div>
        );
    }
}

const documentTemplateCategoryProps = {
    id: PropTypes.string.isRequired,
};

const projectEstimateProps = {
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    grandTotal: PropTypes.number.isRequired,
};

CreateDocument.propTypes = {
    dispatch: PropTypes.func.isRequired,
    projectId: PropTypes.string.isRequired,
    projectHasDrawing: PropTypes.bool,
    documentTemplateCategories: PropTypes.arrayOf(
        PropTypes.shape(documentTemplateCategoryProps)
    ).isRequired,
    estimates: PropTypes.arrayOf(PropTypes.shape(projectEstimateProps))
        .isRequired,
    officeId: PropTypes.number.isRequired,
    currentUserRoleIds: PropTypes.arrayOf(PropTypes.number).isRequired,
};

CreateDocument.defaultProps = {
    projectHasDrawing: false,
};

function mapStateToProps(state) {
    const {
        organization,
        projectHome,
        documentTemplateCategories: documentTemplateCategoriesState,
        projectEstimates,
        currentUser,
    } = state;
    const { isFetching } = state.documents;
    const { currentProject } = projectHome;
    const { hasDrawing, officeId } = currentProject;
    const { documentTemplateCategories } = documentTemplateCategoriesState;
    const { data: estimates } = projectEstimates;
    const { userRoleIds: currentUserRoleIds } = currentUser;
    return {
        isFetching,
        organization,
        projectHasDrawing: hasDrawing,
        documentTemplateCategories,
        estimates,
        officeId,
        currentUserRoleIds,
    };
}

export default connect(mapStateToProps)(CreateDocument);
