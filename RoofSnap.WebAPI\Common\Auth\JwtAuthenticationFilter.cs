﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Security.Principal;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web.Http.Filters;
using IdentityModel;
using Microsoft.IdentityModel.Tokens;

namespace RoofSnap.WebAPI.Common.Auth
{
    public class JwtAuthenticationFilter : IAuthenticationFilter
    {
        public bool AllowMultiple => false;

        public Task AuthenticateAsync(HttpAuthenticationContext context, CancellationToken cancellationToken)
        {
            var controllerName = context.ActionContext.ControllerContext.ControllerDescriptor.ControllerName;

#if DEBUG
            if (controllerName.Equals("Home", StringComparison.Ordinal))
            {
                context.Principal = new GenericPrincipal(new GenericIdentity("Home"), new string[] { });
                return Task.CompletedTask;
            }
#endif

            if (controllerName.Equals("Homeowner", StringComparison.Ordinal))
            {
                context.Principal = new GenericPrincipal(new GenericIdentity("Homeowner"), new string[] { });
                return Task.CompletedTask;
            }

            if (context.Request.Headers.TryGetValues("Stripe-Signature", out _))
            {
                // Allow the request through and set a principal for Stripe requests
                var principal = new GenericPrincipal(new GenericIdentity("Stripe", "Header"), new string[] { "StripeWebhook" });
                context.Principal = principal;

                return Task.CompletedTask;
            }
            
            if (!TryGetBearerTokenFromAuthorizationHeader(context.Request, out string tokenString) &&
                !TryGetBearerTokenFromCookie(context.Request, out tokenString))
            {
                context.ErrorResult = new AuthenticationFailureResult("No credentials present.", context.Request);
                return Task.CompletedTask;
            }

            var tokenHandler = new JwtSecurityTokenHandler();
            if (!tokenHandler.CanReadToken(tokenString))
            {
                context.ErrorResult = new AuthenticationFailureResult("Credentials not valid. Could not read token.", context.Request);
                return Task.CompletedTask;
            }

            JwtSecurityToken token = tokenHandler.ReadJwtToken(tokenString);
            JwtResult jwtResult = token.SignatureAlgorithm == "HS256" ? CheckOldJwtToken(tokenString) : CheckNewJwtToken(tokenString);

            if (!jwtResult.Authenticated)
            {
                context.ErrorResult = new AuthenticationFailureResult("Credentials not valid.", context.Request);
                return Task.CompletedTask;
            }

            if (jwtResult.UserId.HasValue)
            {
                // JWT auths user

                var principal = new ClaimsPrincipal(new GenericIdentity(jwtResult.UserName, "JWT"));
                principal.AddIdentity(new ClaimsIdentity(new[]
                {
                    new Claim("userId", jwtResult.UserId.ToString()),
                    new Claim("organizationId", jwtResult.OrganizationId.ToString()),
                    new Claim("userName", jwtResult.UserName),
                    new Claim("jwtAuthToken", tokenString),
                }));
                context.Principal = principal;
            }
            else if (jwtResult.ServiceClientId != null)
            {
                // JWT auths service

                var principal =
                    new GenericPrincipal(new GenericIdentity(jwtResult.ServiceClientId, "JWT"), new string[0]);
                principal.AddIdentity(new ClaimsIdentity(new[]
                {
                    new Claim("jwtAuthToken", tokenString),
                }));
                context.Principal = principal;
            }

            return Task.CompletedTask;
        }

        public Task ChallengeAsync(HttpAuthenticationChallengeContext context, CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }

        private static JwtResult CheckNewJwtToken(string tokenString)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            JwtSecurityToken token = tokenHandler.ReadJwtToken(tokenString);
            if (!RoofSnapIdentityServerDiscovery.JsonWebKeys.TryGetValue(token.Header.Kid, out IdentityModel.Jwk.JsonWebKey jwk))
                return JwtResult.Unauthenticated;

            var validationParameters = new TokenValidationParameters
            {
                ValidIssuer = RoofSnapIdentityServerDiscovery.DiscoveryResponse.Issuer,
                ValidAudience = "RoofSnapApi",
                IssuerSigningKey = new RsaSecurityKey(new RSAParameters{Exponent = Base64Url.Decode(jwk.E), Modulus = Base64Url.Decode(jwk.N)})
                {
                    KeyId = jwk.Kid
                }
            };
            try
            {
                // Throws exception if token is invalid
                tokenHandler.ValidateToken(tokenString, validationParameters, out _);

                var jwtResult = new JwtResult {Authenticated = true};

                if (token.Subject != null)
                {
                    // If the token has a 'sub', then it identifies a user
                    jwtResult.UserId = int.Parse(token.Subject);
                    jwtResult.UserName = token.Payload["name"].ToString();
                    jwtResult.OrganizationId = long.Parse(token.Payload["orgId"].ToString());
                }
                else if (token.Payload.ContainsKey("client_id"))
                {
                    // If the token has a 'client_id', then it identifies a service
                    jwtResult.ServiceClientId = token.Payload["client_id"].ToString();
                }

                return jwtResult;
            }
            catch (Exception)
            {
                return JwtResult.Unauthenticated;
            }
        }

        private static JwtResult CheckOldJwtToken(string tokenString)
        {
            // TODO: Validation parameters could be static
            var securityKey =
                new SymmetricSecurityKey(
                    Encoding.Default.GetBytes(ConfigurationManager.AppSettings["JwtSymmetricSecurityKey"]));
            var signingCredentials = new SigningCredentials(securityKey, "HS256");

            // This should be temporary code as we transition from https://roofsnap-dev-auth.azurewebsites.net to https://auth2.roofsnap.com as the 'ValidIssuer'
            // We'll come back after tokens coming from tne old issuer have expired.
            string[] validIssuers = ConfigurationManager.AppSettings["JwtIssuer"].Split(',');

            JwtResult result = CheckTokenWithIssuer(tokenString, signingCredentials, validIssuers[0]);
            if (result.Authenticated || validIssuers.Length == 1)
                return result;

            return CheckTokenWithIssuer(tokenString, signingCredentials, validIssuers[1]);
        }

        private static JwtResult CheckTokenWithIssuer(string tokenString, SigningCredentials signingCredentials,
            string validIssuer)
        {
            var validationParameters = new TokenValidationParameters
            {
                IssuerSigningKey = signingCredentials.Key,
                ValidAudience = ConfigurationManager.AppSettings["JwtAudienceId"],
                ValidIssuer = validIssuer
            };

            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                tokenHandler.ValidateToken(tokenString, validationParameters, out _);
                JwtSecurityToken jwtSecurityToken = tokenHandler.ReadJwtToken(tokenString);
                string userIdString = jwtSecurityToken.Payload["name"].ToString();
                string organizationIdString = jwtSecurityToken.Payload["OrganizationId"].ToString();
                var jwtResult = new JwtResult
                {
                    UserName = jwtSecurityToken.Payload.Sub,
                    UserId = int.Parse(userIdString),
                    Authenticated = true,
                    OrganizationId = long.Parse(organizationIdString)
                };

                return jwtResult;
            }
            catch (Exception)
            {
                return JwtResult.Unauthenticated;
            }
        }

        private static bool TryGetBearerTokenFromCookie(HttpRequestMessage request, out string token)
        {
            token = string.Empty;
            CookieState jwtTokenCookie = request.Headers.GetCookies().FirstOrDefault()?.Cookies.FirstOrDefault(cookie => cookie.Name == "Authorization");

            if (jwtTokenCookie == null)
                return false;

            try
            {
                string[] jwtTokenCookieValues = jwtTokenCookie.Value.Split(' ');
                if (jwtTokenCookieValues[0].Contains("Bearer"))
                {
                    token = jwtTokenCookieValues[1];
                    return true;
                }
            }
            catch
            {
                return false;
            }

            return false;
        }

        private static bool TryGetBearerTokenFromAuthorizationHeader(HttpRequestMessage request, out string token)
        {
            token = string.Empty;
            IEnumerable<string> headerVals;
            
            if (!request.Headers.TryGetValues("Authorization", out headerVals))
                return false;

            try
            {
                string authHeader = headerVals.First();
                string[] authHeaderTokens = authHeader.Split(' ');
                if (authHeaderTokens[0].Contains("Bearer"))
                {
                    token = authHeaderTokens[1];
                    return true;
                }
            }
            catch
            {
                return false;
            }

            return false;
        }
    }
}