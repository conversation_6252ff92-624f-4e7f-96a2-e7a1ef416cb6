﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AllForOne" version="1.0.0.27" targetFramework="net472" />
  <package id="BoDi" version="1.4.1" targetFramework="net472" />
  <package id="Bogus" version="29.0.2" targetFramework="net472" />
  <package id="Braintree" version="4.8.0" targetFramework="net472" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net472" />
  <package id="FileHelpers" version="3.2.7" targetFramework="net472" />
  <package id="FluentAssertions" version="4.19.4" targetFramework="net472" />
  <package id="Flurl" version="2.8.0" targetFramework="net472" />
  <package id="Flurl.Http" version="2.4.0" targetFramework="net472" />
  <package id="GeoJSON.Net" version="0.1.51" targetFramework="net472" />
  <package id="GeoJSON.Net.Contrib.MsSqlSpatial" version="0.2.0" targetFramework="net472" />
  <package id="Gherkin" version="6.0.0" targetFramework="net472" />
  <package id="HalClient.Net" version="3.1.0" targetFramework="net472" />
  <package id="IdentityModel" version="2.0.1" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.Azure.EventGrid" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Azure.KeyVault.Core" version="1.0.0" targetFramework="net472" />
  <package id="Microsoft.Azure.Services.AppAuthentication" version="1.3.1" targetFramework="net472" />
  <package id="Microsoft.Azure.Storage.Common" version="10.0.3" targetFramework="net472" />
  <package id="Microsoft.Azure.Storage.Queue" version="10.0.3" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Clients.ActiveDirectory" version="4.3.0" targetFramework="net472" />
  <package id="Microsoft.Rest.ClientRuntime" version="2.3.12" targetFramework="net472" />
  <package id="Microsoft.Rest.ClientRuntime.Azure" version="3.3.13" targetFramework="net472" />
  <package id="Microsoft.SqlServer.Types" version="14.0.1016.290" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net472" />
  <package id="NUnit" version="3.12.0" targetFramework="net472" />
  <package id="NUnit.Console" version="3.10.0" targetFramework="net472" />
  <package id="NUnit.ConsoleRunner" version="3.10.0" targetFramework="net472" />
  <package id="NUnit.Extension.NUnitProjectLoader" version="3.6.0" targetFramework="net472" />
  <package id="NUnit.Extension.NUnitV2Driver" version="3.7.0" targetFramework="net472" />
  <package id="NUnit.Extension.NUnitV2ResultWriter" version="3.6.0" targetFramework="net472" />
  <package id="NUnit.Extension.TeamCityEventListener" version="1.0.7" targetFramework="net472" />
  <package id="NUnit.Extension.VSProjectLoader" version="3.8.0" targetFramework="net472" />
  <package id="NUnit3TestAdapter" version="3.16.1" targetFramework="net472" developmentDependency="true" />
  <package id="OpenPop" version="2.0.6.2" targetFramework="net472" />
  <package id="RoofSnap.Events.Core" version="1.0.314" targetFramework="net472" />
  <package id="SpecFlow" version="3.0.225" targetFramework="net472" />
  <package id="SpecFlow.NUnit" version="3.0.225" targetFramework="net472" />
  <package id="SpecFlow.Tools.MsBuild.Generation" version="3.0.225" targetFramework="net472" />
  <package id="System.Net.Http" version="4.3.2" targetFramework="net472" />
  <package id="System.Reflection.Emit" version="4.3.0" targetFramework="net472" />
  <package id="System.Reflection.Emit.Lightweight" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net472" requireReinstallation="true" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.4.0" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.4.0" targetFramework="net472" requireReinstallation="true" />
  <package id="Tavis.UriTemplates" version="1.1.1" targetFramework="net472" />
  <package id="Utf8Json" version="1.3.7" targetFramework="net472" requireReinstallation="true" />
  <package id="WindowsAzure.Storage" version="9.0.0" targetFramework="net472" />
</packages>