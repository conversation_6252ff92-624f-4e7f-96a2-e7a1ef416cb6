﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;

namespace RoofSnap.WebAPI.Common.Auth
{
    public class ApiKeyAuthorizeFilter : IAuthorizationFilter
    {
        public bool AllowMultiple => false;

        public Task<HttpResponseMessage> ExecuteAuthorizationFilterAsync(HttpActionContext actionContext, CancellationToken cancellationToken,
            Func<Task<HttpResponseMessage>> continuation)
        {
#if DEBUG
            if (actionContext.Request.RequestUri.AbsolutePath == "/")
                return continuation();
#endif

            if (actionContext.Request.Headers.Any(x => x.Key == "Stripe-Signature"))
                return continuation();

            string apiKey = ConfigurationManager.AppSettings["apiKey"];
            IEnumerable<string> requestApiKeys;
            CookieState apiKeyCookie = actionContext.Request.Headers.GetCookies().FirstOrDefault()?.Cookies.FirstOrDefault(cookie => cookie.Name == "X-Api-Key");
            if (!actionContext.Request.Headers.TryGetValues("X-Api-Key", out requestApiKeys) && apiKeyCookie == null)
            {
                actionContext.Response = new HttpResponseMessage(HttpStatusCode.Unauthorized);
                return Task.FromResult(actionContext.Response);
            }

            if (requestApiKeys?.FirstOrDefault() != apiKey && apiKeyCookie?.Value != apiKey)
            {
                actionContext.Response = new HttpResponseMessage(HttpStatusCode.Forbidden);
                return Task.FromResult(actionContext.Response);
            }

            return continuation();
        }
    }
}