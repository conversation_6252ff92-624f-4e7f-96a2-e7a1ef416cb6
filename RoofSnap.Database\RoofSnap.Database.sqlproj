﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>RoofSnap.Database</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{6cee2e2c-c6d6-49a1-951f-4e86f2c3dc6f}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath />
    <RootNamespace>RoofSnap.Database</RootNamespace>
    <AssemblyName>RoofSnap.Database</AssemblyName>
    <ModelCollation>1033,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <DefaultCollation>SQL_Latin1_General_CP1_CI_AS</DefaultCollation>
    <DefaultFilegroup>PRIMARY</DefaultFilegroup>
    <AutoUpdateStatisticsAsynchronously>True</AutoUpdateStatisticsAsynchronously>
    <QueryStoreMaxStorageSize>256</QueryStoreMaxStorageSize>
    <DbScopedConfigMaxDOP>8</DbScopedConfigMaxDOP>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="roofsnaplive\" />
    <Folder Include="dbo\" />
    <Folder Include="dbo\Tables\" />
    <Folder Include="roofsnaplive\Views\" />
    <Folder Include="dbo\Views\" />
    <Folder Include="Storage\" />
    <Folder Include="Security\" />
    <Folder Include="hashids\" />
    <Folder Include="hashids\Functions\" />
    <Folder Include="hashids\User Defined Types\" />
    <Folder Include="roofsnaplive\Functions\" />
    <Folder Include="roofsnaplive\Tables\" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="roofsnaplive\Tables\Project.sql" />
    <Build Include="roofsnaplive\Tables\Labels.sql" />
    <Build Include="roofsnaplive\Tables\AppVersionStatus.sql" />
    <Build Include="roofsnaplive\Tables\NearmapTokens.sql" />
    <Build Include="roofsnaplive\Tables\OfficePricedChargeableItemRelationships.sql" />
    <Build Include="roofsnaplive\Tables\NearmapOrderBillings.sql" />
    <Build Include="roofsnaplive\Tables\NearmapOrders.sql" />
    <Build Include="roofsnaplive\Tables\ProjectEstimateOption.sql" />
    <Build Include="roofsnaplive\Tables\AppHeartBeat.sql" />
    <Build Include="roofsnaplive\Tables\SharedProject.sql" />
    <Build Include="roofsnaplive\Tables\OfficeChargeCategories.sql" />
    <Build Include="roofsnaplive\Tables\officeCustomProjectStatus.sql" />
    <Build Include="dbo\Tables\RoofSnapProjects.sql" />
    <Build Include="roofsnaplive\Tables\StripePaymentRequest.sql" />
    <Build Include="roofsnaplive\Tables\StripePaymentStatus.sql" />
    <Build Include="roofsnaplive\Tables\Template.sql" />
    <Build Include="roofsnaplive\Tables\UserOffices.sql" />
    <Build Include="roofsnaplive\Tables\Offices.sql" />
    <Build Include="roofsnaplive\Tables\TemplateChargeableItem.sql" />
    <Build Include="roofsnaplive\Tables\Categories.sql" />
    <Build Include="roofsnaplive\Tables\ChargeableItems.sql" />
    <Build Include="roofsnaplive\Tables\ProjectMeasurements.sql" />
    <Build Include="roofsnaplive\Tables\OfficePricedChargeableItemHiddenColors.sql" />
    <Build Include="dbo\Tables\webpages_OAuthMembership.sql" />
    <Build Include="roofsnaplive\Tables\ProjectEstimateItems.sql" />
    <Build Include="dbo\Tables\webpages_Membership.sql" />
    <Build Include="roofsnaplive\Tables\OfficeHiddenImages.sql" />
    <Build Include="dbo\Tables\webpages_Roles.sql" />
    <Build Include="roofsnaplive\Tables\ProjectDrawing.sql" />
    <Build Include="dbo\Tables\webpages_UsersInRoles.sql" />
    <Build Include="roofsnaplive\Tables\ChargeableItemColors.sql" />
    <Build Include="roofsnaplive\Tables\Documents.sql" />
    <Build Include="roofsnaplive\Tables\DocumentTemplates.sql" />
    <Build Include="dbo\Tables\__MigrationHistory.sql" />
    <Build Include="roofsnaplive\Tables\Orders.sql" />
    <Build Include="roofsnaplive\Tables\BlobBlobs.sql" />
    <Build Include="roofsnaplive\Tables\OrderStatus.sql" />
    <Build Include="dbo\Tables\webpages_UserProfiles.sql" />
    <Build Include="roofsnaplive\Tables\ChargeableItemRelationships.sql" />
    <Build Include="roofsnaplive\Tables\OrderReportType.sql" />
    <Build Include="roofsnaplive\Tables\ProjectImage.sql" />
    <Build Include="roofsnaplive\Tables\CustomOrganizationResources.sql" />
    <Build Include="roofsnaplive\Tables\OfficeChargeableItemLabels.sql" />
    <Build Include="roofsnaplive\Tables\Organizations.sql" />
    <Build Include="roofsnaplive\Tables\OfficePricedChargeableItems.sql" />
    <Build Include="roofsnaplive\Tables\ProjectDocument.sql" />
    <Build Include="roofsnaplive\Tables\OrganizationTransactions.sql" />
    <Build Include="roofsnaplive\Tables\OrderBilling.sql" />
    <Build Include="roofsnaplive\Tables\OfficeCustomCategories.sql" />
    <Build Include="roofsnaplive\Tables\OfficePricedChargeableItemColorOptions.sql" />
    <Build Include="roofsnaplive\Tables\OfficeDocuments.sql" />
    <Build Include="roofsnaplive\Tables\OfficeCategorySettings.sql" />
    <Build Include="roofsnaplive\Tables\ProjectStatus.sql" />
    <Build Include="roofsnaplive\Views\vOfficePricedChargeableItems2.sql" />
    <Build Include="dbo\Views\vOfficeCategories2.sql" />
    <Build Include="roofsnaplive\Views\Roles.sql" />
    <Build Include="roofsnaplive\Views\UsersInRoles.sql" />
    <Build Include="roofsnaplive\Views\OrganizationTemplateChargeableItem.sql" />
    <Build Include="roofsnaplive\Views\UserProfiles.sql" />
    <Build Include="dbo\Views\vOfficePricedChargeableItems3.sql" />
    <Build Include="roofsnaplive\Views\vOfficePricedChargeableItems3.sql" />
    <Build Include="dbo\Views\vColors.sql" />
    <Build Include="dbo\Views\vOfficeCategories.sql" />
    <Build Include="roofsnaplive\Views\vOfficePricedChargeableItems.sql" />
    <Build Include="roofsnaplive\Views\MaterialSearchCategory.sql" />
    <Build Include="roofsnaplive\Views\MaterialSearchItem.sql" />
    <Build Include="roofsnaplive\Views\vUserProfiles.sql" />
    <Build Include="dbo\Views\OfficeCategories.sql" />
    <Build Include="roofsnaplive\Views\ProjectsAndSharedProjects.sql" />
    <Build Include="dbo\Views\vOfficePricedChargeableItems4.sql">
      <AnsiNulls>Off</AnsiNulls>
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="roofsnaplive\Views\vOfficePricedChargeableItems4.sql" />
    <Build Include="roofsnaplive\Views\FixedOfficePricedChargeableItems.sql" />
    <Build Include="roofsnaplive\Views\FixedOfficeCustomCategories.sql" />
    <Build Include="roofsnaplive\Views\FixedCategories.sql" />
    <Build Include="roofsnaplive\Views\SubMaterialRelationships.sql" />
    <Build Include="roofsnaplive\Views\SubMaterialRelationships2.sql" />
    <Build Include="roofsnaplive\Views\FixedProjectEstimateItems.sql" />
    <Build Include="roofsnaplive\Views\vCategories.sql" />
    <Build Include="Storage\OrganizationCatalog.sql" />
    <Build Include="Storage\UserProfilesCatalog.sql" />
    <Build Include="FullTextIndexes.sql" />
    <Build Include="Security\Jesse.sql" />
    <Build Include="Security\Jesse_1.sql" />
    <Build Include="Security\ChaosMonkey.sql" />
    <Build Include="Security\ChaosMonkey_1.sql" />
    <Build Include="Security\RoleMemberships.sql" />
    <Build Include="hashids\Functions\consistentShuffle.sql" />
    <Build Include="hashids\Functions\hash.sql" />
    <Build Include="hashids\Functions\encode1.sql" />
    <Build Include="hashids\Functions\encode2.sql" />
    <Build Include="hashids\Functions\encodeList.sql" />
    <Build Include="hashids\Functions\encodeSplit.sql" />
    <Build Include="hashids\User Defined Types\ListOfBigint.sql" />
    <Build Include="hashids\User Defined Types\ListOfInt.sql" />
    <Build Include="Security\hashids.sql" />
    <Build Include="Security\roofsnap_test.sql" />
    <Build Include="Security\roofsnaplive.sql" />
    <Build Include="setupNewUser.sql" />
    <Build Include="Storage\ProjectCatalog.sql" />
    <Build Include="roofsnaplive\Tables\OrganizationStatuses.sql" />
    <Build Include="roofsnaplive\Views\UserProfileExtended.sql" />
    <Build Include="roofsnaplive\Tables\MeasurementOrders.sql" />
    <Build Include="roofsnaplive\Tables\MeasurementOrderProviders.sql" />
    <Build Include="dbo\Tables\NearmapAvailabilitySamples.sql" />
    <Build Include="roofsnaplive\Tables\OrganizationCredits.sql" />
    <Build Include="roofsnaplive\Tables\OrganizationCreditTransactions.sql" />
    <Build Include="roofsnaplive\Tables\OrganizationSettings.sql" />
    <Build Include="roofsnaplive\Tables\OrganizationSettingTypes.sql" />
    <None Include="SetUpInitialOrganizationSettingTypes.sql" />
    <Build Include="roofsnaplive\Tables\SketchOrderTiers.sql" />
    <Build Include="roofsnaplive\Tables\SketchTechPaymentAmounts.sql" />
    <Build Include="roofsnaplive\Tables\SketchOrderCosts.sql" />
    <Build Include="roofsnaplive\Tables\OrganizationOverrideSketchOrderTiers.sql" />
    <Build Include="roofsnaplive\Tables\SketchOrderTiersBySquare.sql" />
    <Build Include="roofsnaplive\Tables\SketchOrderTiersByFacet.sql" />
    <Build Include="roofsnaplive\Tables\CreditRatesBySubscription.sql" />
    <Build Include="roofsnaplive\Tables\OrganizationOverrideCreditRates.sql" />
    <None Include="SetUpInitialSketchOrderTiers.sql" />
    <Build Include="roofsnaplive\Tables\SubscriptionTypes.sql" />
    <None Include="SetUpInitialRoles.sql" />
    <None Include="SetUpInitialStripePaymentStatuses.sql" />
    <None Include="SetUpSubscriptionsAndCreditsRates.sql" />
    <Build Include="roofsnaplive\Tables\OverrideSketchTechPayTiers.sql" />
    <None Include="SetUpInitialSketchOrderStatuses.sql" />
    <Build Include="roofsnaplive\Tables\WebHookEventTypes.sql" />
    <Build Include="roofsnaplive\Tables\WebHookSubscriptions.sql" />
    <Build Include="SketchAdminPriorityFunction.sql" />
    <Build Include="SketchTechPriorityFunction.sql" />
    <Build Include="roofsnaplive\Tables\OfficeDefaultEdgeSubTypes.sql" />
    <Build Include="roofsnaplive\Tables\EdgeTypes.sql" />
    <Build Include="roofsnaplive\Tables\EdgeSubTypes.sql" />
    <None Include="SetUpEdgeTypeData.sql" />
    <None Include="SetUpEdgeSubTypeData.sql" />
    <Build Include="DefaultOfficeEdgeTypesForOrganization.sql" />
    <Build Include="roofsnaplive\Tables\OrderPaymentType.sql" />
    <Build Include="roofsnaplive\Tables\SketchOrderBillingTransactions.sql" />
    <Build Include="roofsnaplive\Tables\SketchOrderCostLevels.sql" />
    <None Include="SetUpInitialSketchOrderCostLevelsData.sql" />
    <Build Include="roofsnaplive\Tables\Currencies.sql" />
    <None Include="SetUpInitialCurrencyData.sql" />
    <Build Include="roofsnaplive\Tables\SketchOptions.sql" />
    <None Include="SetUpInitialSketchOptionsData.sql" />
    <Build Include="roofsnaplive\Tables\SketchOrderReportingOptions.sql" />
    <Build Include="roofsnaplive\Tables\SketchOrderReportingOptionTypes.sql" />
    <None Include="SetUpInitialSketchOrderReportingOptionTypes.sql" />
    <Build Include="roofsnaplive\Tables\BraintreeSubscriptionIds.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingParseVertexes.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingParseEdges.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingParseFaces.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingParsePins.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingFacetCount.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingFacetLabelCount.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingLineLabelCount.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingPinCount.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingPinWithImageCount.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingPinWithNoteCount.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingSublineLabelCount.sql" />
    <Build Include="roofsnaplive\Views\ProjectEngagements.sql" />
    <Build Include="roofsnaplive\Views\ProjectMeasurementsAtPitchValueEngagements.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingFacetWithPitchCount.sql" />
    <Build Include="roofsnaplive\Views\NearmapProjectEngagements.sql" />
    <Build Include="roofsnaplive\Tables\NotificationPriorityLevels.sql" />
    <Build Include="roofsnaplive\Tables\NotificationTypes.sql" />
    <Build Include="roofsnaplive\Tables\NotificationTemplates.sql" />
    <Build Include="roofsnaplive\Tables\Notifications.sql" />
    <Build Include="roofsnaplive\Tables\SketchOrderDeliveryNotifications.sql" />
    <Build Include="roofsnaplive\Tables\OrganizationOverrideNotificationPriorityLevels.sql" />
    <Build Include="roofsnaplive\Tables\NotificationAcknowledgements.sql" />
    <None Include="SetUpInitialSketchOrderDeliveryOptionsData.sql" />
    <Build Include="roofsnaplive\Tables\SketchOrderDeliveryOptions.sql" />
    <Build Include="roofsnaplive\Tables\OrganizationFreeSketchOrderRates.sql" />
    <Build Include="roofsnaplive\Tables\OrganizationFreeSketchOrderBalances.sql" />
    <Build Include="roofsnaplive\Tables\DocumentV2Templates.sql" />
    <Build Include="roofsnaplive\Tables\DocumentDataContextTypes.sql" />
    <Build Include="roofsnaplive\Tables\DocumentsV2.sql" />
    <Build Include="roofsnaplive\Tables\DocumentV2Renderings.sql" />
    <None Include="SetUpInitialNotificationTemplates.sql" />
    <Build Include="roofsnaplive\Tables\DocumentV2TemplateCategories.sql" />
    <Build Include="roofsnaplive\Tables\DocumentV2TemplatePermissions.sql" />
    <Build Include="roofsnaplive\Tables\EstimateRules.sql" />
    <Build Include="roofsnaplive\Tables\BraintreeSubscriptionRecords.sql" />
    <Build Include="roofsnaplive\Tables\BraintreeTransactionRecords.sql" />
    <Build Include="roofsnaplive\Tables\ReceiptLog.sql" />
    <Build Include="SetupNewUserV2.sql" />
    <Build Include="dbo\AzureSQLMaintenance.sql" />
    <Build Include="roofsnaplive\Tables\BlueRoof.sql" />
    <Build Include="roofsnaplive\Functions\ProjectDrawingEdgeCount.sql" />
    <Build Include="roofsnaplive\Tables\DocumentV2ExternalImplementerValues.sql" />
    <Build Include="roofsnaplive\Tables\NotificationCustomDisplayTemplates.sql" />
    <Build Include="roofsnaplive\Tables\SketchOrderGroups.sql" />
    <Build Include="dbo\Tables\statsBefore.sql" />
    <Build Include="dbo\Tables\idxBefore.sql" />
    <Build Include="dbo\Tables\AzureSQLMaintenanceLog.sql" />
    <Build Include="roofsnaplive\Views\PayGoCustomers.sql" />
    <Build Include="roofsnaplive\Tables\OfficeV2ContractTerms.sql" />
    <Build Include="CopySampleOfficeSettings.sql" />
    <Build Include="roofsnaplive\Tables\RushOrderSettings.sql" />
    <None Include="SetUpInitialSketchOrderCostsData.sql" />
    <None Include="SetUpInitialOrderReportType.sql" />
    <None Include="SetupInitialSketchOrderTiersByFacet.sql" />
    <None Include="UpdateSketchOrderTiers.sql" />
    <None Include="SetUpInitialDocumentV2TemplatesData.sql" />
    <Build Include="roofsnaplive\Tables\SketchOrderRevisionNotes.sql" />
    <Build Include="roofsnaplive\Tables\EdgeStatus.sql" />
    <Build Include="roofsnaplive\Tables\ProjectSnapEstimates.sql" />
    <Build Include="roofsnaplive\Tables\ProjectSnapEstimatesLineItemsMaster.sql" />
  </ItemGroup>
  <ItemGroup>
    <PostDeploy Include="PostDeploymentScripts.sql" />
    <None Include="SetUpInitialOrganizationStatuses.sql" />
    <None Include="SetUpInitialEdgeStatuses.sql" />
  </ItemGroup>
  <ItemGroup>
    <RefactorLog Include="RoofSnap.Database.refactorlog" />
  </ItemGroup>
  <ItemGroup>
    <None Include="SetUpInitialMeasurementOrderProviders.sql" />
    <None Include="SetUpInitialWebHookEventTypes.sql" />
    <None Include="SetUpInitialOrderPaymentTypes.sql" />
    <None Include="SetUpInitialSubscriptionTypesName.sql" />
    <None Include="SetUpInitialNotificationTypes.sql" />
    <None Include="SetUpInitialNotificationPriorityLevels.sql" />
    <None Include="SetUpInitialDocumentDataContextTypes.sql" />
    <None Include="SetUpInitialProjectSnapEstimatesLineItemsMaster.sql" />
  </ItemGroup>
</Project>