﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata;
using RoofSnap.Core.Data.Configurations;
using RoofSnap.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace RoofSnap.Core.Data
{
    public class RoofSnapDbContext : DbContext, IRoofSnapData
    {
        private readonly Func<IEnumerable<EntityEntry>, Task> _onBeforeSavingFunc;

        public DbSet<AppVersionStatusModel> AppVersionStatuses { get; set; }
        public DbSet<UserProfileModel> UserProfiles { get; set; }
        public DbSet<OrganizationModel> Organizations { get; set; }
        public DbSet<SketchOrderModel> SketchOrders { get; set; }
        public DbSet<OrderBillingModel> OrderBillings { get; set; }
        public DbSet<ProjectDrawingModel> ProjectDrawings { get; set; }
        public DbSet<ProjectModel> Projects { get; set; }
        public DbSet<ProjectMeasurementsModel> ProjectMeasurements { get; set; }
        public DbSet<ProjectImageModel> ProjectImages { get; set; }
        public DbSet<ProjectDocumentModel> ProjectDocuments { get; set; }
        public DbSet<OfficeModel> Offices { get; set; }
        public DbSet<UserOfficeModel> UserOffices { get; set; }
        public DbSet<SharedProjectModel> SharedProjects { get; set; }
        public DbSet<RoleModel> Roles { get; set; }
        public DbSet<UserRoleModel> UserRoles { get; set; }
        public DbSet<EstimateOptionModel> EstimateOptions { get; set; }
        public DbSet<MeasurementOrderModel> MeasurementOrders { get; set; }
        public DbSet<OrganizationCreditModel> OrganizationCredits { get; set; }
        public DbSet<OrganizationCreditTransactionModel> OrganizationCreditTransactions { get; set; }
        public DbSet<OrganizationSettingsModel> OrganizationSettings { get; set; }
        public DbSet<SubscriptionTypeModel> SubscriptionTypes { get; set; }
        public DbSet<CreditRateBySubscriptionModel> CreditRatesBySubscription { get; set; }
        public DbSet<OrganizationOverrideCreditRateModel> OrganizationOverrideCreditRates { get; set; }
        public DbSet<OrganizationOverrideSketchCostTiersModel> OrganizationOverrideSketchCostTiers { get; set; }
        public DbSet<SketchOrderCostsModel> SketchOrderCosts { get; set; }
        public DbSet<SketchOrderTierModel> SketchOrderTiers { get; set; }
        public DbSet<SketchOrderTiersByFacetModel> SketchOrderTiersByFacet { get; set; }
        public DbSet<SketchOrderTiersBySquareModel> SketchOrderTiersBySquare { get; set; }
        public DbSet<SketchTechPaymentAmountsModel> SketchTechPaymentAmounts { get; set; }
        public DbSet<WebHookSubscriptionModel> WebHookSubscriptions { get; set; }
        public DbSet<OfficeDefaultEdgeSubTypeModel> OfficeDefaultEdgeSubTypes { get; set; }
        public DbSet<SketchOrderBillingTransactionModel> SketchOrderBillingTransactions { get; set; }
        public DbSet<SketchOrderReportingOptionModel> SketchOrderReportingOptions { get; set; }
        public DbSet<BraintreeSubscriptionIdModel> BraintreeSubscriptionIds { get; set; }
        public DbQuery<ProjectEngagementModel> ProjectEngagements { get; set; }
        public DbQuery<NearmapProjectEngagementModel> NearmapProjectEngagements { get; set; }
        public DbQuery<ProjectMeasurementsAtPitchValueEngagementsModel> ProjectMeasurementsAtPitchValueEngagements { get; set; }
        public DbSet<NotificationTemplateModel> NotificationTemplates { get; set; }
        public DbSet<NotificationCustomDisplayTemplateModel> NotificationCustomDisplayTemplates { get; set; }
        public DbSet<NotificationModel> Notifications { get; set; }
        public DbSet<NotificationAcknowledgementModel> NotificationAcknowledgements { get; set; }
        public DbSet<SketchOrderDeliveryNotificationModel> SketchOrderDeliveryNotifications { get; set; }
        public DbSet<OrganizationOverrideNotificationPriorityLevelModel> OrganizationOverrideNotificationPriorityLevels { get; set; }
        public DbSet<OrganizationFreeSketchOrderRateModel> OrganizationFreeSketchOrderRates { get; set; }
        public DbSet<OrganizationFreeSketchOrderBalanceModel> OrganizationFreeSketchOrderBalances { get; set; }
        public DbSet<DocumentV2TemplateModel> DocumentTemplates { get; set; }
        public DbSet<DocumentV2TemplateCategoryModel> DocumentTemplateCategories { get; set; }
        public DbSet<DocumentV2Model> Documents { get; set; }
        public DbSet<DocumentV2RenderingModel> DocumentRenderings { get; set; }
        public DbSet<DocumentV2ExternalImplementerValueModel> DocumentExternalImplementerValues { get; set; }
        public DbSet<DocumentV2TemplatePermissionsModel> DocumentTemplatePermissions { get; set; }
        public DbSet<EstimateRuleModel> EstimateRules { get; set; }
        public DbSet<BraintreeSubscriptionRecordModel> BraintreeSubscriptionRecords { get; set; }
        public DbSet<BraintreeTransactionRecordModel> BraintreeTransactionRecords { get; set; }
        public DbSet<ReceiptLogModel> ReceiptLogs { get; set; }
        public DbSet<NearmapOrderModel> NearmapOrders { get; set; }
        public DbSet<NearmapOrderBillingModel> NearmapOrderBillings { get; set; }
        public DbSet<BlueRoofOrderModel> BlueRoofOrders { get; set; }
        public DbSet<SketchOrderGroupModel> SketchOrderGroups { get; set; }
        public DbQuery<OfficeContractTermsModel> OfficeContractTerms { get; set; }
        public DbSet<OfficeV2ContractTermsModel> OfficeV2ContractTerms { get; set; }
        public DbSet<EstimateTemplateModel> EstimateTemplates { get; set; }
        public DbSet<EstimateTemplateItemModel> EstimateTemplateItems { get; set; }

        public DbSet<OfficePricedChargeableItemModel> OfficePricedChargeableItems { get; set; }
        public DbSet<RushOrderSettingsModel> RushOrderSettings { get; set; }

        public DbSet<SketchOrderRevisionNoteModel> SketchOrderRevisionNotes { get; set; }

        public DbSet<StripePaymentRequest> StripePaymentRequests { get; set; }

        public DbSet<ProjectSnapEstimateModel> ProjectSnapEstimates { get; set; }
        public DbSet<ProjectSnapEstimatesLineItemsMasterModel> ProjectSnapEstimatesLineItemsMaster { get; set; }

        /// <param name="dbContextOptions"></param>
        /// <param name="onBeforeSavingFunc">Callback allowing consumer to inspect tracked entities before the base SaveChangesAsync method is called.</param>
        public RoofSnapDbContext(DbContextOptions<RoofSnapDbContext> dbContextOptions, Func<IEnumerable<EntityEntry>, Task> onBeforeSavingFunc = null)
            : base(dbContextOptions)
        {
            _onBeforeSavingFunc = onBeforeSavingFunc;
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new AppVersionStatusConfiguration());
            modelBuilder.ApplyConfiguration(new UserProfileConfiguration());
            modelBuilder.ApplyConfiguration(new OrganizationConfiguration());
            modelBuilder.ApplyConfiguration(new SketchOrderConfiguration());
            modelBuilder.ApplyConfiguration(new OrderBillingConfiguration());
            modelBuilder.ApplyConfiguration(new ProjectDrawingConfiguration());
            modelBuilder.ApplyConfiguration(new ProjectConfiguration());
            modelBuilder.ApplyConfiguration(new ProjectMeasurementsConfiguration());
            modelBuilder.ApplyConfiguration(new ProjectImageConfiguration());
            modelBuilder.ApplyConfiguration(new ProjectDocumentConfiguration());
            modelBuilder.ApplyConfiguration(new OfficeConfiguration());
            modelBuilder.ApplyConfiguration(new UserOfficeConfiguration());
            modelBuilder.ApplyConfiguration(new SharedProjectConfiguration());
            modelBuilder.ApplyConfiguration(new RoleConfiguration());
            modelBuilder.ApplyConfiguration(new UserRoleConfiguration());
            modelBuilder.ApplyConfiguration(new EstimateOptionsConfiguration());
            modelBuilder.ApplyConfiguration(new MeasurementOrderConfiguration());
            modelBuilder.ApplyConfiguration(new OrganizationCreditConfiguration());
            modelBuilder.ApplyConfiguration(new OrganizationCreditTransactionConfiguration());
            modelBuilder.ApplyConfiguration(new OrganizationSettingsConfiguration());
            modelBuilder.ApplyConfiguration(new OrganizationOverrideSketchCostTiersConfiguration());
            modelBuilder.ApplyConfiguration(new SketchOrderCostsConfiguration());
            modelBuilder.ApplyConfiguration(new SketchOrderTiersConfiguration());
            modelBuilder.ApplyConfiguration(new SketchOrderTiersBySquareConfiguration());
            modelBuilder.ApplyConfiguration(new SketchOrderTiersByFacetConfiguration());
            modelBuilder.ApplyConfiguration(new SketchTechPaymentAmountsConfiguration());
            modelBuilder.ApplyConfiguration(new CreditRateBySubscriptionConfiguration());
            modelBuilder.ApplyConfiguration(new SubscriptionTypeConfiguration());
            modelBuilder.ApplyConfiguration(new OverrideOrganizationCreditRateConfiguration());
            modelBuilder.ApplyConfiguration(new WebHookSubscriptionConfiguration());
            modelBuilder.ApplyConfiguration(new OfficeDefaultEdgeSubTypeConfiguration());
            modelBuilder.ApplyConfiguration(new SketchOrderBillingTransactionConfiguration());
            modelBuilder.ApplyConfiguration(new SketchOrderReportingOptionsConfiguration());
            modelBuilder.ApplyConfiguration(new BraintreeSubscriptionIdConfiguration());
            modelBuilder.ApplyConfiguration(new ProjectEngagementConfiguration());
            modelBuilder.ApplyConfiguration(new NearmapProjectEngagementsConfiguration());
            modelBuilder.ApplyConfiguration(new ProjectMeasurementsAtPitchValueEngagementsConfiguration());
            modelBuilder.ApplyConfiguration(new NotificationTemplateConfiguration());
            modelBuilder.ApplyConfiguration(new NotificationCustomDisplayTemplateConfiguration());
            modelBuilder.ApplyConfiguration(new NotificationConfiguration());
            modelBuilder.ApplyConfiguration(new SketchOrderDeliveryNotificationConfiguration());
            modelBuilder.ApplyConfiguration(new OrganizationOverrideNotificationPriorityLevelConfiguration());
            modelBuilder.ApplyConfiguration(new NotificationAcknowledgementConfiguration());
            modelBuilder.ApplyConfiguration(new OrganizationFreeSketchOrderRateConfiguration());
            modelBuilder.ApplyConfiguration(new OrganizationFreeSketchOrderBalanceConfiguration());
            modelBuilder.ApplyConfiguration(new DocumentV2TemplateConfiguration());
            modelBuilder.ApplyConfiguration(new DocumentV2Configuration());
            modelBuilder.ApplyConfiguration(new DocumentsV2RenderingConfiguration());
            modelBuilder.ApplyConfiguration(new DocumentV2TemplateCategoryConfiguration());
            modelBuilder.ApplyConfiguration(new DocumentV2TemplatePermissionsConfiguration());
            modelBuilder.ApplyConfiguration(new EstimateRuleConfiguration());
            modelBuilder.ApplyConfiguration(new BraintreeSubscriptionRecordConfiguration());
            modelBuilder.ApplyConfiguration(new BraintreeTransactionRecordConfiguration());
            modelBuilder.ApplyConfiguration(new ReceiptLogConfiguration());
            modelBuilder.ApplyConfiguration(new NearmapOrderConfiguration());
            modelBuilder.ApplyConfiguration(new NearmapOrderBillingConfiguration());
            modelBuilder.ApplyConfiguration(new BlueRoofConfiguration());
            modelBuilder.ApplyConfiguration(new DocumentV2ExternalValueConfiguration());
            modelBuilder.ApplyConfiguration(new OfficeContractTermsConfiguration());
            modelBuilder.ApplyConfiguration(new SketchOrderGroupConfiguration());
            modelBuilder.ApplyConfiguration(new OfficeV2ContractTermsConfiguration());
            modelBuilder.ApplyConfiguration(new EstimateTemplateConfiguration());
            modelBuilder.ApplyConfiguration(new EstimateTemplateItemConfiguration());
            modelBuilder.ApplyConfiguration(new OfficePricedChargeableItemConfiguration());
            modelBuilder.ApplyConfiguration(new RushOrderSettingsConfiguration());
            modelBuilder.ApplyConfiguration(new SketchOrderRevisionNoteConfiguration());
            modelBuilder.ApplyConfiguration(new StripePaymentRequestConfiguration());
            modelBuilder.ApplyConfiguration(new ProjectSnapEstimatesConfiguration());
            modelBuilder.ApplyConfiguration(new ProjectSnapEstimatesLineItemsMasterConfiguration());
        }

        public void SetVersion<TEntity>(TEntity entity, byte[] version) where TEntity : class
        {
            EntityEntry<TEntity> entry = Entry(entity);
            entry.OriginalValues["Version"] = version;
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            await OnBeforeSavingAsync();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private async Task OnBeforeSavingAsync()
        {
            foreach (EntityEntry<ProjectDocumentModel> projectDocumentEntry in ChangeTracker.Entries<ProjectDocumentModel>())
                await ApplySoftDeleteAsync(projectDocumentEntry);
            foreach (EntityEntry<ProjectModel> projectEntry in ChangeTracker.Entries<ProjectModel>())
                await ApplySoftDeleteAsync(projectEntry);
            foreach (EntityEntry<OfficeModel> officeEntry in ChangeTracker.Entries<OfficeModel>())
                await ApplySoftDeleteAsync(officeEntry);
            foreach (EntityEntry<EstimateOptionsConfiguration> estimateOptionEntry in ChangeTracker.Entries<EstimateOptionsConfiguration>())
                await ApplySoftDeleteAsync(estimateOptionEntry);
            foreach (EntityEntry<DocumentV2Model> documentEntry in ChangeTracker.Entries<DocumentV2Model>())
                await ApplySoftDeleteAsync(documentEntry);
            foreach (EntityEntry<DocumentV2RenderingModel> documentRenderingEntry in ChangeTracker.Entries<DocumentV2RenderingModel>())
                await ApplySoftDeleteAsync(documentRenderingEntry);

            if (_onBeforeSavingFunc != null)
                await _onBeforeSavingFunc(ChangeTracker.Entries());
        }

        private async Task ApplySoftDeleteAsync(EntityEntry entry)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.CurrentValues["__deleted"] = false;
                    break;

                case EntityState.Deleted:
                    entry.State = EntityState.Modified;
                    entry.CurrentValues["__deleted"] = true;

                    await SoftDeleteChildrenAsync(entry);
                    break;
            }
        }

        private async Task SoftDeleteChildrenAsync(EntityEntry entry)
        {
            IEnumerable<NavigationEntry> childNavigationEntries = entry.Navigations.Where(n => !n.Metadata.IsDependentToPrincipal());
            foreach (NavigationEntry navigationEntry in childNavigationEntries)
            {
                Type dependentType = navigationEntry.Metadata.ForeignKey.PrincipalToDependent.FieldInfo.FieldType;
                if (dependentType.IsGenericType && dependentType.GetGenericTypeDefinition() == typeof(ICollection<>))
                    dependentType = dependentType.GetGenericArguments()[0];

                IEntityType dependentEntityType = Model.FindEntityType(dependentType);
                IProperty deletedProperty = dependentEntityType.GetProperties()
                    .FirstOrDefault(p => p.Relational().ColumnName == "__deleted");
                if (deletedProperty != null)
                {
                    string tableName = dependentEntityType.Relational().TableName;
                    string tableSchema = dependentEntityType.Relational().Schema;

                    IProperty foreignKeyProperty = navigationEntry.Metadata.ForeignKey.Properties.Single();

                    string keyName = entry.Metadata.FindPrimaryKey().Properties.Select(x => x.Name).Single();
                    object entity = entry.Entity;
                    // ReSharper disable once PossibleNullReferenceException
                    object keyValue = entity.GetType().GetProperty(keyName).GetValue(entity, null);

                    await ExecuteSqlCommandAsync(
                        $"UPDATE [{tableSchema}].[{tableName}] SET __deleted = 1 WHERE {foreignKeyProperty.Name} = {{0}}",
                        keyValue);
                }
            }
        }

        public Task<int> ExecuteSqlCommandAsync(RawSqlString sql, params object[] parameters)
        {
            return Database.ExecuteSqlCommandAsync(sql, parameters);
        }
    }
}
