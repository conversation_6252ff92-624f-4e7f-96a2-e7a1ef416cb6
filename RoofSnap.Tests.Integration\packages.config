﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AllForOne" version="1.0.0.27" targetFramework="net472" />
  <package id="AutoMapper" version="5.2.0" targetFramework="net472" />
  <package id="Braintree" version="4.8.0" targetFramework="net472" />
  <package id="Castle.Core" version="4.2.0" targetFramework="net472" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net472" />
  <package id="FluentAssertions" version="4.19.4" targetFramework="net472" />
  <package id="GeoAPI.CoordinateSystems" version="1.7.5" targetFramework="net472" />
  <package id="GeoAPI.Core" version="1.7.5" targetFramework="net472" />
  <package id="GeoJSON.Net" version="0.1.51" targetFramework="net472" />
  <package id="Handlebars.Net" version="1.9.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.Azure.EventGrid" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Azure.Services.AppAuthentication" version="1.3.1" targetFramework="net472" />
  <package id="Microsoft.Bcl" version="1.1.10" targetFramework="net472" />
  <package id="Microsoft.Bcl.Build" version="1.0.21" targetFramework="net472" />
  <package id="Microsoft.CSharp" version="4.4.0" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.Abstractions" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.Analyzers" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.Relational" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.SqlServer" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.Extensions.Caching.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Caching.Memory" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Clients.ActiveDirectory" version="4.3.0" targetFramework="net472" />
  <package id="Microsoft.Net.Http" version="2.2.29" targetFramework="net472" />
  <package id="Microsoft.Rest.ClientRuntime" version="2.3.19" targetFramework="net472" />
  <package id="Microsoft.Rest.ClientRuntime.Azure" version="3.3.19" targetFramework="net472" />
  <package id="Moq" version="4.7.137" targetFramework="net472" />
  <package id="NetTopologySuite" version="1.15.3" targetFramework="net472" />
  <package id="NetTopologySuite.CoordinateSystems" version="1.15.3" targetFramework="net472" />
  <package id="NetTopologySuite.Core" version="1.15.3" targetFramework="net472" />
  <package id="NetTopologySuite.IO.SqlServerBytes" version="1.15.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net472" />
  <package id="Ninject" version="3.3.4" targetFramework="net472" />
  <package id="NUnit" version="3.8.1" targetFramework="net472" />
  <package id="NUnit3TestAdapter" version="3.16.1" targetFramework="net472" developmentDependency="true" />
  <package id="Remotion.Linq" version="2.2.0" targetFramework="net472" />
  <package id="RestSharp" version="105.2.3" targetFramework="net472" />
  <package id="RoofSnap.Events.Core" version="1.0.314" targetFramework="net472" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net472" />
  <package id="System.Collections" version="4.0.11" targetFramework="net472" />
  <package id="System.Collections.Immutable" version="1.5.0" targetFramework="net472" />
  <package id="System.ComponentModel.Annotations" version="4.5.0" targetFramework="net472" />
  <package id="System.Data.SqlClient" version="4.6.0" targetFramework="net472" />
  <package id="System.Diagnostics.Debug" version="4.0.11" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.5.0" targetFramework="net472" />
  <package id="System.Interactive.Async" version="3.2.0" targetFramework="net472" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.Linq" version="4.1.0" targetFramework="net472" />
  <package id="System.Linq.Expressions" version="4.1.0" targetFramework="net472" />
  <package id="System.Linq.Queryable" version="4.0.1" targetFramework="net472" />
  <package id="System.Memory" version="4.5.1" targetFramework="net472" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.4.0" targetFramework="net472" />
  <package id="System.ObjectModel" version="4.0.12" targetFramework="net472" />
  <package id="System.Reflection" version="4.1.0" targetFramework="net472" />
  <package id="System.Reflection.Extensions" version="4.0.1" targetFramework="net472" />
  <package id="System.Runtime" version="4.3.1" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.1" targetFramework="net472" />
  <package id="System.Runtime.Extensions" version="4.1.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading" version="4.0.11" targetFramework="net472" />
  <package id="Tavis.UriTemplates" version="1.1.1" targetFramework="net472" />
</packages>