﻿/*
 Pre-Deployment Script Template							
--------------------------------------------------------------------------------------
 This file contains SQL statements that will be executed before the build script.	
 Use SQLCMD syntax to include a file in the pre-deployment script.			
 Example:      :r .\myfile.sql								
 Use SQLCMD syntax to reference a variable in the pre-deployment script.		
 Example:      :setvar TableName MyTable							
               SELECT * FROM [$(TableName)]					
--------------------------------------------------------------------------------------
*/

:r .\SetUpInitialOrganizationStatuses.sql
:r .\SetUpInitialMeasurementOrderProviders.sql
:r .\SetUpInitialOrganizationSettingTypes.sql
:r .\SetUpInitialOrderReportType.sql
:r .\SetUpInitialSketchOrderTiers.sql
:r .\UpdateSketchOrderTiers.sql
:r .\SetUpInitialSketchOrderTiersByFacet.sql
:r .\SetUpInitialSketchOrderCostLevelsData.sql
:r .\SetUpInitialSketchOrderCostsData.sql
:r .\SetUpInitialRoles.sql
:r .\SetUpSubscriptionsAndCreditsRates.sql
:r .\SetUpInitialSketchOrderStatuses.sql
:r .\SetUpInitialWebHookEventTypes.sql
:r .\SetUpEdgeTypeData.sql
:r .\SetUpEdgeSubTypeData.sql
:r .\SetUpInitialOrderPaymentTypes.sql
:r .\SetUpInitialSketchOptionsData.sql
:r .\SetUpInitialSketchOrderReportingOptionTypes.sql
:r .\SetUpInitialSubscriptionTypesName.sql
:r .\SetUpInitialSketchOrderDeliveryOptionsData.sql
:r .\SetUpInitialNotificationTypes.sql
:r .\SetUpInitialNotificationPriorityLevels.sql
:r .\SetUpInitialNotificationTemplates.sql
:r .\SetUpInitialDocumentDataContextTypes.sql
:r .\SetUpInitialDocumentV2TemplatesData.sql
:r .\SetUpInitialEdgeStatuses.sql
:r .\SetUpInitialStripePaymentStatuses.sql
:r .\SetUpInitialProjectSnapEstimatesLineItemsMaster.sql