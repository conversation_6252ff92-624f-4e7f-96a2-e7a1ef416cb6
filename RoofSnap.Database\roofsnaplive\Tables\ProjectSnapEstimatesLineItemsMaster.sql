﻿CREATE TABLE [RoofSnaplive].[ProjectSnapEstimatesLineItemsMaster] (
    [id]               INT                IDENTITY (1, 1) NOT NULL,
    [roofMaterialName] NVARCHAR (100)     NOT NULL,
    [name]             NVARCHAR (100)     NOT NULL,
    [description]      NVARCHAR (MAX)     NOT NULL,
    [__createdAt]      DATETIMEOFFSET (3) CONSTRAINT [DF_ProjectSnapEstimatesLineItemsMaster___createdAt] DEFAULT (CONVERT([DATETIMEOFFSET](3), Sysutcdatetime(), (0))) NOT NULL,
    [__updatedAt]      DATETIMEOFFSET (3) CONSTRAINT [DF_ProjectSnapEstimatesLineItemsMaster___updatedAt] DEFAULT Sysdatetimeoffset() NULL,
    CONSTRAINT [PK_ProjectSnapEstimatesLineItemsMaster] PRIMARY KEY CLUSTERED ([id] ASC)
)
go