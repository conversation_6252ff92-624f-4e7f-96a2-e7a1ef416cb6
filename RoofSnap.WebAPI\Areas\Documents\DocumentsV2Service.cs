﻿using HtmlAgilityPack;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using RoofSnap.Core.Data;
using RoofSnap.Core.Models;
using RoofSnap.Functions.Models;
using RoofSnap.WebAPI.Areas.Documents.Exceptions;
using RoofSnap.WebAPI.Areas.Documents.InputReplacementChain;
using RoofSnap.WebAPI.Areas.Documents.Templates;
using RoofSnap.WebAPI.Areas.DocumentTemplates;
using RoofSnap.WebAPI.Areas.Projects.ProjectDocuments;
using RoofSnap.WebAPI.Areas.SketchOrders.SketchOSWorkflow;
using RoofSnap.WebAPI.Common;
using RoofSnap.WebAPI.Common.BlobStorage;
using RoofSnap.WebAPI.Common.Data;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace RoofSnap.WebAPI.Areas.Documents
{
    public interface IDocumentsV2Service
    {
        Task<string> GetHtmlAsync(string documentShortCode, bool decorateForHomeOwner = false);
        Task<string> GetFormFilledHtmlAsync(string documentShortCode);
        Task RenderingCallback(string documentShortCode, string renderingShortCode, CreateDocumentCallback callback);
        Task<ServiceResult<DocumentV2Model>> CreateAsync(DocumentV2Model document, string dataContextEntityId);
        Task<ServiceResult<DocumentV2Model>> UpdateAsync(DocumentV2Model document, byte[] version);
        Task<ServiceResult<DocumentV2Model>> CreateForUserAsync(DocumentV2Model document, string dataContextEntityId);
        Task<DocumentV2RenderingModel> CreateRenderingAsync(DocumentV2Model document, string fileName = null, bool? isSigned = null, bool addESignLink = false);
        Task<SharedAccessToken> CreateSharedAccessTokenAsync(string documentShortCode, string documentRenderingShortCode);
        Task<ServiceResult<DocumentV2Model>> GetAsync(string documentShortCode);
        Task<ServiceResult<DocumentV2RenderingModel>> GetRenderingAsync(string documentShortCode, string documentRenderingShortCode);
        Task DeleteAsync(string documentShortCode);
        Task DeleteRenderingAsync(string documentShortCode, string renderingShortCode);
        Task CloneInProgressDocumentsAsync(string projectId, string clonedProjectId);
    }

    public class DocumentsV2Service : IDocumentsV2Service
    {
        private readonly IDocumentData _documentData;
        private readonly IDocumentTemplateFactory _documentTemplateFactory;
        private readonly IRoofSnapData _roofSnapData;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IDocumentGeneratorQueueService _documentGeneratorQueueService;
        private readonly IDocumentRenderingFileName _documentRenderingFileName;
        private readonly IDocumentStorage _documentStorage;
        private readonly ICreateDocumentRequestFactory _createDocumentRequestFactory;
        private readonly IOrganizationStorageFactory _organizationStorageFactory;
        private readonly ISketchOrderResponseSender _sketchOrderResponseSender;
        private readonly IProjectDocumentService _projectDocumentService;
        private readonly IDocumentTemplatesV2Service _documentTemplatesV2Service;
        private readonly IHtmlDocumentHandler _htmlDocumentHandler;
        private readonly IBlueRoofStorage _blueRoofStorage;

        public DocumentsV2Service(
            IDocumentTemplateFactory documentTemplateFactory,
            IDocumentData documentData,
            IRoofSnapData roofSnapData,
            IUnitOfWork unitOfWork,
            IDocumentGeneratorQueueService documentGeneratorQueueService,
            IDocumentRenderingFileName documentRenderingFileName,
            IDocumentStorage documentStorage,
            ICreateDocumentRequestFactory createDocumentRequestFactory,
            IOrganizationStorageFactory organizationStorageFactory,
            ISketchOrderResponseSender sketchOrderResponseSender,
            IProjectDocumentService projectDocumentService,
            IDocumentTemplatesV2Service documentTemplatesV2Service,
            IHtmlDocumentHandler htmlDocumentHandler,
            IBlueRoofStorage blueRoofStorage)
        {
            _documentTemplateFactory = documentTemplateFactory;
            _documentData = documentData;
            _roofSnapData = roofSnapData;
            _unitOfWork = unitOfWork;
            _documentGeneratorQueueService = documentGeneratorQueueService;
            _documentRenderingFileName = documentRenderingFileName;
            _documentStorage = documentStorage;
            _createDocumentRequestFactory = createDocumentRequestFactory;
            _organizationStorageFactory = organizationStorageFactory;
            _sketchOrderResponseSender = sketchOrderResponseSender;
            _projectDocumentService = projectDocumentService;
            _documentTemplatesV2Service = documentTemplatesV2Service;
            _htmlDocumentHandler = htmlDocumentHandler;
            _blueRoofStorage = blueRoofStorage;
        }

        public async Task<ServiceResult<DocumentV2Model>> CreateAsync(DocumentV2Model document,
            string dataContextEntityId)
        {
            await SaveDocumentEntityAsync(document, dataContextEntityId);

            if (string.IsNullOrWhiteSpace(document.Name)) // Name not provided by client
                document.Name = await GenerateDefaultDocumentName(document);

            return new ServiceResult<DocumentV2Model>(_roofSnapData.Documents.Where(d => d.Id == document.Id));
        }

        public async Task<ServiceResult<DocumentV2Model>> UpdateAsync(DocumentV2Model document, byte[] version)
        {
            _roofSnapData.SetVersion(document, version);

            await _roofSnapData.SaveChangesAsync();
            return new ServiceResult<DocumentV2Model>(_roofSnapData.Documents
                .Where(p => p.Id == document.Id));
        }

        private async Task SaveDocumentEntityAsync(DocumentV2Model document, string dataContextEntityId)
        {
            if (string.IsNullOrWhiteSpace(document.Name))
                document.Name = string.Empty;
            await _roofSnapData.Documents.AddAsync(document);

            await _roofSnapData.Entry(document)
                .Reference(d => d.Template)
                .LoadAsync();

            if (document.Template == null)
                throw new EntityNotFoundException<DocumentV2TemplateModel>(document.TemplateId);

            switch (document.Template.DocumentDataContextType)
            {
                case DocumentDataContextType.MultiEstimate:
                    if (!document.EstimateOptionIds.Any())
                        throw new EstimateOptionsRequiredException();

                    IList<EstimateOptionModel> estimateOptionModels =
                        await _roofSnapData
                            .EstimateOptions
                            .Include(p => p.Project)
                            .Where(p => document.EstimateOptionIds.Contains(p.Id))
                            .ToListAsync();

                    if (!estimateOptionModels.Any())
                        throw new EntityNotFoundException<EstimateOptionModel>(document.EstimateOptionIds.First());

                    document.Project = estimateOptionModels.First().Project;
                    break;
                case DocumentDataContextType.ProjectDrawing:
                    ProjectModel project = await _roofSnapData.Projects
                        .Include(p => p.ProjectDrawing)
                        .FirstOrDefaultAsync(p => p.ShortCode == dataContextEntityId || p.Id == dataContextEntityId);
                    if (project == null)
                        throw new EntityNotFoundException<ProjectModel>(dataContextEntityId);

                    if (project.ProjectDrawing == null)
                        throw new ProjectDrawingRequiredException($"A project drawing for project {project.Id} is required.", project.Id);

                    document.Project = project;
                    break;
                case DocumentDataContextType.Estimate:
                    EstimateOptionModel estimateOptionModel =
                        await _roofSnapData
                            .EstimateOptions
                            .Include(p => p.Project)
                            .FirstOrDefaultAsync(p => p.Id == dataContextEntityId);

                    if (estimateOptionModel == null)
                        throw new EntityNotFoundException<EstimateOptionModel>(dataContextEntityId);

                    document.Project = estimateOptionModel.Project;
                    document.EstimateOption = estimateOptionModel;
                    break;
                default:
                    throw new NotImplementedException();
            }
            await _roofSnapData.SaveChangesAsync();
        }

        private async Task<string> GenerateDefaultDocumentName(DocumentV2Model document)
        {
            // Default to the template name
            var result = document.Template.FileName;
            result = result.Replace("Classic ", ""); // customers don't like the word classic in their document file names

            // If it's a 'project' document calculate a revision number
            if (document.ProjectId != null)
            {
                var v2Count = await _roofSnapData.Documents
                    .IgnoreQueryFilters() // include soft-deleted in count
                    .Include(d => d.Template)
                    .CountAsync(d =>
                        d.ProjectId == document.ProjectId &&
                        (d.Template.FileName == result || d.Template.FileName == $"Classic {result}"));

                var v1Count = 0;

                if (document.Template.IsGlobal)
                {
                    v1Count = await _roofSnapData.ProjectDocuments
                        .IgnoreQueryFilters() // Ignore soft-deleted in count
                        .CountAsync(d =>
                            d.ProjectId == document.ProjectId &&
                            !d.IsV2Copy &&
                            GlobalDocumentTemplates.ClassicNameToCategoryNameDictionary.ContainsKey(document.Template.FileName) &&
                            d.Category == GlobalDocumentTemplates.ClassicNameToCategoryNameDictionary[document.Template.FileName]);
                }

                var totalCount = v1Count + v2Count;
                if (totalCount > 1)
                    result = $"{result} (Revision {totalCount})";
            }
            await _roofSnapData.SaveChangesAsync();
            return result;
        }

        public async Task<ServiceResult<DocumentV2Model>> CreateForUserAsync(DocumentV2Model document, string dataContextEntityId)
        {
            await SaveDocumentEntityAsync(document, dataContextEntityId);

            if (!await _documentTemplatesV2Service.UserCanCreateDocumentAsync(document.Template.ShortCode,
                document.Project.OfficeId.Value))
            {
                throw new UserCanNotCreateDocumentException($"User does not have permission to create document from template: {document.Template.ShortCode}", document.Template.ShortCode);
            }

            if (string.IsNullOrWhiteSpace(document.Name)) // Name not provided by client
                document.Name = await GenerateDefaultDocumentName(document);

            return new ServiceResult<DocumentV2Model>(_roofSnapData.Documents.Where(d => d.Id == document.Id));
        }

        public async Task<DocumentV2RenderingModel> CreateRenderingAsync(DocumentV2Model document, string fileName = null, bool? isSigned = null, bool addESignLink = false)
        {
            var rendering = new DocumentV2RenderingModel
            {
                IsGenerated = false, // A callback will set this to true after the pdf is actually generated
                OrganizationId = document.OrganizationId,
                Document = document,
                FileName = await _documentRenderingFileName.GenerateAsync(document, fileName, isSigned),
                IsSigned = isSigned ?? false,
                ESignLink = addESignLink ? $"/projects/{document.ProjectId}/documents/sign/{document.ShortCode}" : null,
            };

            await _roofSnapData.DocumentRenderings.AddAsync(rendering);
            await _roofSnapData.SaveChangesAsync();

            string blobName = await _documentStorage.GetBlobNameAsync(rendering);
            rendering.BlobName = blobName;

            CreateDocumentRequest createDocumentRequest = await _createDocumentRequestFactory.CreateAsync(rendering);

            // The unit of work should be committed before the document queue is allowed to start working on it.
            _unitOfWork.AddPostCommitAction(() => _documentGeneratorQueueService.EnqueueAsync(createDocumentRequest));

            return rendering;
        }

        public async Task<string> GetFormFilledHtmlAsync(string documentShortCode)
        {
            DocumentV2Model document = await _roofSnapData
                .Documents
                .Include(d => d.Template)
                .FirstOrDefaultAsync(d => d.ShortCode == documentShortCode);
            if (document == null)
                throw new EntityNotFoundException<DocumentV2Model>(documentShortCode);

            string html = await DecorateDocumentHtmlAsync(document);


            HtmlDocument htmlDocument = _htmlDocumentHandler.Create(html);

            if (document.Template.HasHtmlForm)
            {
                var formData = JsonConvert.DeserializeObject<Dictionary<string, string>>(document.HtmlFormData);
                _htmlDocumentHandler.ReplaceInputTags(htmlDocument, formData);
            }
            htmlDocument.Sanitize();

            return htmlDocument.DocumentNode.OuterHtml;
        }

        public async Task<string> GetHtmlAsync(string documentShortCode, bool decorateForHomeOwner = false)
        {
            DocumentV2Model document = await _roofSnapData
                .Documents
                .Include(d => d.Template)
                .FirstOrDefaultAsync(d => d.ShortCode == documentShortCode);
            if (document == null)
                throw new EntityNotFoundException<DocumentV2Model>(documentShortCode);

            return await DecorateDocumentHtmlAsync(document, decorateForHomeOwner);
        }

        public async Task RenderingCallback(string documentShortCode, string renderingShortCode, CreateDocumentCallback callback)
        {
            DocumentV2RenderingModel rendering = await _roofSnapData.DocumentRenderings
                .Include(r => r.Document)
                .Include(r => r.Document.Template)
                    .ThenInclude(t => t.TemplateCategory)
                .Include(r => r.Document.Project)
                .Include(r => r.Document.Project.SketchOrder)
                .Include(r => r.Document.ExternalImplementerValue)
                .IgnoreQueryFilters() // allow the callback to work for deleted documents / renderings
                .FirstOrDefaultAsync(r => r.ShortCode == renderingShortCode);
            if (rendering == null)
                throw new EntityNotFoundException<DocumentV2RenderingModel>(renderingShortCode);
            if (rendering.Document?.ShortCode != documentShortCode)
                throw new EntityNotFoundException<DocumentV2Model>(documentShortCode);

            // Don't do this: rendering.IsGenerated = callback.Success
            // Once a pdf has been generated, IsGenerated should always be true.
            if (callback.Success)
                rendering.IsGenerated = true;

            // 'temporary' code to copy the document to old blob storage so v1 clients can still get them
            // ReSharper disable once PossibleNullReferenceException
            if (rendering.Document.Template.IsGlobal
                && rendering.Document?.ProjectId != null
                && rendering.Document.Template.FileName != GlobalDocumentTemplates.SketchOrderReportContinentalMapping
                && rendering.Document.Template.FileName != GlobalDocumentTemplates.MarketSharpSketchReport)
            {
                IOrganizationStorage organizationStorage = _organizationStorageFactory.Create(rendering.OrganizationId);
                var documentUrl = await organizationStorage.GetBlobUriAsync(rendering.BlobName);

                string projectDocumentCategory = GlobalDocumentTemplates.ClassicNameToCategoryNameDictionary[rendering.Document.Template.FileName];
                var projectDocument = new ProjectDocumentModel
                {
                    Name = rendering.Document.Name,
                    Category = projectDocumentCategory,
                    DocumentUrl = documentUrl.ToString(), // the mobile proxy will get a SaSToken for this
                    ProjectId = rendering.Document.ProjectId,
                    IsV2Copy = true
                };
                _roofSnapData.ProjectDocuments.Add(projectDocument);
            }
            if (rendering.Document.Template.TemplateCategory.Name == GlobalDocumentTemplates.SketchOrderReport
                && rendering.Document.Template.FileName != GlobalDocumentTemplates.SketchOrderReportContinentalMapping
                && rendering.Document.Template.FileName != GlobalDocumentTemplates.MarketSharpSketchReport)
            {
                long sketchOrderId = rendering.Document.Project.SketchOrder.Id;
                _sketchOrderResponseSender.AddPostCommitActionToSendEmail(sketchOrderId);
            }
            if (rendering.Document.Template.FileName == GlobalDocumentTemplates.SketchOrderReportContinentalMapping)
            {
                IOrganizationStorage organizationStorage = _organizationStorageFactory.Create(rendering.OrganizationId);
                var sasToken =
                    await organizationStorage.GetSasReadUrlAsync(rendering.BlobName, DateTimeOffset.Now.AddDays(1));

                await _roofSnapData.Entry(rendering.Document.Project.SketchOrder)
                    .Reference(so => so.BlueRoofOrder)
                    .LoadAsync();
                var blobName =
                    $"{rendering.Document.Project.SketchOrder.BlueRoofOrder.BatchName}\\{rendering.Document.Project.SketchOrder.ProjectName}.pdf";
                await _blueRoofStorage.CopyDocumentToBlueRoofStorageAsync(blobName, sasToken);
            }

            // TODO: This is an ugly hack to move SketchOrderReports to the Sketch Order category
            // so they'll show up on iOS. This should be removed once iOS can handle document template permissions.
            if (rendering.Document.Template.TemplateCategoryId == 3)
                rendering.Document.TemplateId = 3;
        }

        public async Task<SharedAccessToken> CreateSharedAccessTokenAsync(string documentShortCode, string documentRenderingShortCode)
        {
            if (Guid.TryParse(documentShortCode, out _)) //if the id is a guid check v1 project documents
            {
                return await GetV1SasTokenAsync(documentShortCode);
            }

            if (!await _documentTemplatesV2Service.UserCanCreateDocumentSasTokenAsync(documentRenderingShortCode))
                throw new UserCanNotOpenDocumentException($"User does not have permission to open documents with template: {documentRenderingShortCode}.", documentRenderingShortCode);

            DocumentV2RenderingModel documentRendering = await _roofSnapData.DocumentRenderings
                .Include(r => r.Document)
                .FirstOrDefaultAsync(r => r.ShortCode == documentRenderingShortCode && r.Document.ShortCode == documentShortCode);

            if (documentRendering == null)
                throw new EntityNotFoundException<DocumentV2RenderingModel>(documentRenderingShortCode);

            return await _documentStorage.CreateSasTokenAsync(documentRendering);
        }

        private async Task<SharedAccessToken> GetV1SasTokenAsync(string projectDocumentId)
        {
            var projectDocument = await _roofSnapData.ProjectDocuments
                .FindAsync(projectDocumentId);
            if (projectDocument == null)
                throw new EntityNotFoundException<DocumentV2RenderingModel>(projectDocumentId);

            var sasToken = await _documentStorage.CreateSasTokenForV1ProjectDocumentAsync(projectDocument);
            return sasToken;
        }

        public async Task<ServiceResult<DocumentV2Model>> GetAsync(string documentShortCode)
        {
            if (!await _roofSnapData.Documents.AnyAsync(d => d.ShortCode == documentShortCode))
                throw new EntityNotFoundException<DocumentV2Model>(documentShortCode);

            return new ServiceResult<DocumentV2Model>(_roofSnapData.Documents.Where(d => d.ShortCode == documentShortCode));
        }

        public async Task<ServiceResult<DocumentV2RenderingModel>> GetRenderingAsync(string documentShortCode, string documentRenderingShortCode)
        {
            if (!await _roofSnapData.DocumentRenderings.AnyAsync(r => r.ShortCode == documentRenderingShortCode && r.Document.ShortCode == documentShortCode))
                throw new EntityNotFoundException<DocumentV2Model>(documentShortCode);

            return new ServiceResult<DocumentV2RenderingModel>(_roofSnapData.DocumentRenderings.Where(r =>
                r.ShortCode == documentRenderingShortCode && r.Document.ShortCode == documentShortCode));
        }

        public async Task DeleteAsync(string documentShortCode)
        {
            if (Guid.TryParse(documentShortCode, out _))
            {
                // if this is a V1 project document it will have a guid id instead of short code
                await _projectDocumentService.DeleteProjectDocumentAsync(documentShortCode);
            }
            else
            {
                var document = await _roofSnapData.Documents
                    .FirstOrDefaultAsync(d => d.ShortCode == documentShortCode);

                if (document == null)
                    throw new EntityNotFoundException<DocumentV2Model>(documentShortCode);

                var featuredContracts = await _roofSnapData.Documents
                    .Where(d => d.ProjectId == document.ProjectId && d.IsFeaturedContract)
                    .ToListAsync();

                if (featuredContracts.Any())
                {
                    featuredContracts.ForEach(x => x.IsFeaturedContract = false);

                    await _roofSnapData.SaveChangesAsync();
                }
                
                _roofSnapData.Documents.Remove(document);
            }
        }

        public async Task DeleteRenderingAsync(string documentShortCode, string renderingShortCode)
        {
            DocumentV2Model document = await _roofSnapData.Documents
                .Include(d => d.Renderings)
                .FirstOrDefaultAsync(d => d.ShortCode == documentShortCode);

            if (document == null)
                throw new EntityNotFoundException<DocumentV2Model>(documentShortCode);

            DocumentV2RenderingModel rendering = document.Renderings.FirstOrDefault(r => r.ShortCode == renderingShortCode);
            if (rendering == null)
                throw new EntityNotFoundException<DocumentV2RenderingModel>(renderingShortCode);

            _roofSnapData.DocumentRenderings.Remove(rendering);
        }

        private async Task<string> DecorateDocumentHtmlAsync(DocumentV2Model document, bool decorateForHomeowner = false)
        {
            var dataContext = new DocumentDataContext(document.OrganizationId, document.ShortCode, decorateForHomeowner);
            if (!string.IsNullOrEmpty(document.ProjectId))
                dataContext.ProjectId = document.ProjectId;
            if (!string.IsNullOrEmpty(document.EstimateOptionId))
                dataContext.EstimateOptionId = document.EstimateOptionId;
            dataContext.TemplateId = document.Template.Id;
            if (document.EstimateOptionIds != null)
            {
                // Don't do this: dataContext.MultiEstimateOptionsIds = document.EstimateOptionIds
                // Copying the reference could have unexpected consequences.
                // Instead, copy the values into a new array:
                dataContext.MultiEstimateOptionIds = new string[document.EstimateOptionIds.Length];
                document.EstimateOptionIds.CopyTo(dataContext.MultiEstimateOptionIds, 0);
            }

            IDocumentTemplate template = await _documentTemplateFactory.CreateAsync(document.Template, decorateForHomeowner);
            foreach (string item in await template.GetDataPrefixesAsync())
                dataContext.DataPrefixes.Add(item);

            dynamic data = await _documentData.CreateAsync(dataContext);

            Func<object, string> func = await template.CompileAsync();

            return func(data);
        }

        public async Task CloneInProgressDocumentsAsync(string projectId, string clonedProjectId)
        {
            var documents = _roofSnapData.Documents.Where(x => x.ProjectId == projectId).ToList();
            documents.ForEach(x => x.ProjectId = clonedProjectId);
            await _roofSnapData.SaveChangesAsync();
        }
    }
}