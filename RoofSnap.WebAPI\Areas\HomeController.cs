﻿using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web;
using System.Web.Http;

namespace RoofSnap.WebAPI.Areas
{
    [RoutePrefix("")]
    public class HomeController : ApiController
    {
        [HttpGet]
        [Route("")]
        public IHttpActionResult Get()
        {
            var filePath = HttpContext.Current.Server.MapPath("~/Static/server-started.htm");
            if (!File.Exists(filePath))
                return NotFound();

            var html = File.ReadAllText(filePath);
            var response = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(html)
            };
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("text/html");
            return ResponseMessage(response);
        }
    }
}
