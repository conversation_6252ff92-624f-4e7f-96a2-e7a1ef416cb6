﻿go

declare @initialSettings table (Id int, [Name] nvarchar(255))
insert into @initialSettings 
	(Id, [Name]) 
values 
	(0, 'None'),
	(2, 'AllowSnappingGoogleImagery'),
	(3, 'FreeSketchOrdersIncluded'),
	(4, 'WhiteLabelDocuments'),
	(5, 'AttachPdfOnSketchOrderEmails'),
	(6, 'DisableRushOrdering'),
	(7, 'EnableMetalRoof'),
	(8, 'EnableGutters'),
	(10, 'EnableEasierEstimates'),
	(13, 'EnableSmartEstimates')

insert into roofsnaplive.OrganizationSettingTypes 
(
	Id
	,[Name]
)
select 
	s.Id
	,s.[Name]
from 
	@initialSettings s
where 
	not exists (select null 
				from roofsnaplive.OrganizationSettingTypes t 
				where t.Id = s.Id 
				and t.[Name] = s.[Name])