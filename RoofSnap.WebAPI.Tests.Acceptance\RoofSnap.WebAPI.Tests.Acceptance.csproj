﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\NUnit3TestAdapter.3.16.1\build\net35\NUnit3TestAdapter.props" Condition="Exists('..\packages\NUnit3TestAdapter.3.16.1\build\net35\NUnit3TestAdapter.props')" />
  <Import Project="..\packages\SpecFlow.Tools.MsBuild.Generation.3.0.225\build\SpecFlow.Tools.MsBuild.Generation.props" Condition="Exists('..\packages\SpecFlow.Tools.MsBuild.Generation.3.0.225\build\SpecFlow.Tools.MsBuild.Generation.props')" />
  <Import Project="..\packages\SpecFlow.NUnit.3.0.225\build\SpecFlow.NUnit.props" Condition="Exists('..\packages\SpecFlow.NUnit.3.0.225\build\SpecFlow.NUnit.props')" />
  <Import Project="..\packages\NUnit.3.12.0\build\NUnit.props" Condition="Exists('..\packages\NUnit.3.12.0\build\NUnit.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6D960FCE-BF10-4BD2-9E70-77B12792B69E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RoofSnap.WebAPI.Tests.Acceptance</RootNamespace>
    <AssemblyName>RoofSnap.WebAPI.Tests.Acceptance</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{3AC096D0-A1C2-E12C-1390-A8335801FDAB};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <ReferencePath>$(ProgramFiles)\Common Files\microsoft shared\VSTT\$(VisualStudioVersion)\UITestExtensionPackages</ReferencePath>
    <IsCodedUITest>False</IsCodedUITest>
    <TestProjectType>UnitTest</TestProjectType>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BoDi, Version=1.4.1.0, Culture=neutral, PublicKeyToken=ff7cd5ea2744b496, processorArchitecture=MSIL">
      <HintPath>..\packages\BoDi.1.4.1\lib\net45\BoDi.dll</HintPath>
    </Reference>
    <Reference Include="Bogus, Version=29.0.2.0, Culture=neutral, PublicKeyToken=fa1bb3f3f218129a, processorArchitecture=MSIL">
      <HintPath>..\packages\Bogus.29.0.2\lib\net40\Bogus.dll</HintPath>
    </Reference>
    <Reference Include="Braintree, Version=4.8.0.0, Culture=neutral, PublicKeyToken=31b586f34d3e96c7, processorArchitecture=MSIL">
      <HintPath>..\packages\Braintree.4.8.0\lib\net452\Braintree.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="FileHelpers, Version=3.2.7.0, Culture=neutral, PublicKeyToken=3e0c08d59cc3d657, processorArchitecture=MSIL">
      <HintPath>..\packages\FileHelpers.3.2.7\lib\net45\FileHelpers.dll</HintPath>
    </Reference>
    <Reference Include="FluentAssertions, Version=4.19.4.0, Culture=neutral, PublicKeyToken=33f2691a05b67b6a, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentAssertions.4.19.4\lib\net45\FluentAssertions.dll</HintPath>
    </Reference>
    <Reference Include="FluentAssertions.Core, Version=4.19.4.0, Culture=neutral, PublicKeyToken=33f2691a05b67b6a, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentAssertions.4.19.4\lib\net45\FluentAssertions.Core.dll</HintPath>
    </Reference>
    <Reference Include="Flurl, Version=2.8.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Flurl.2.8.0\lib\net40\Flurl.dll</HintPath>
    </Reference>
    <Reference Include="Flurl.Http, Version=2.4.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Flurl.Http.2.4.0\lib\net46\Flurl.Http.dll</HintPath>
    </Reference>
    <Reference Include="GeoJSON.Net, Version=0.1.51.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoJSON.Net.0.1.51\lib\portable-net40+sl5+wp80+win8+wpa81\GeoJSON.Net.dll</HintPath>
    </Reference>
    <Reference Include="GeoJSON.Net.Contrib.MsSqlSpatial, Version=0.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoJSON.Net.Contrib.MsSqlSpatial.0.2.0\lib\net45\GeoJSON.Net.Contrib.MsSqlSpatial.dll</HintPath>
    </Reference>
    <Reference Include="Gherkin, Version=6.0.0.0, Culture=neutral, PublicKeyToken=86496cfa5b4a5851, processorArchitecture=MSIL">
      <HintPath>..\packages\Gherkin.6.0.0\lib\net45\Gherkin.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis, Version=1.42.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.1.42.0\lib\net45\Google.Apis.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Auth, Version=1.42.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Auth.1.42.0\lib\net45\Google.Apis.Auth.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Auth.PlatformServices, Version=1.42.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Auth.1.42.0\lib\net45\Google.Apis.Auth.PlatformServices.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Core, Version=1.42.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Core.1.42.0\lib\net45\Google.Apis.Core.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.PlatformServices, Version=1.42.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.1.42.0\lib\net45\Google.Apis.PlatformServices.dll</HintPath>
    </Reference>
    <Reference Include="HalClient.Net, Version=3.1.0.21532, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\HalClient.Net.3.1.0\lib\net45\HalClient.Net.dll</HintPath>
    </Reference>
    <Reference Include="Holf.AllForOne, Version=1.0.0.27, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AllForOne.1.0.0.27\lib\Holf.AllForOne.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="IdentityModel, Version=2.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IdentityModel.2.0.1\lib\net45\IdentityModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.EventGrid, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.EventGrid.2.0.0\lib\net452\Microsoft.Azure.EventGrid.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.KeyVault.Core.1.0.0\lib\net40\Microsoft.Azure.KeyVault.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Services.AppAuthentication, Version=1.3.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Services.AppAuthentication.1.3.1\lib\net472\Microsoft.Azure.Services.AppAuthentication.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Storage.Common, Version=10.0.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Storage.Common.10.0.3\lib\net452\Microsoft.Azure.Storage.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Storage.Queue, Version=10.0.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Storage.Queue.10.0.3\lib\net452\Microsoft.Azure.Storage.Queue.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.IdentityModel.Clients.ActiveDirectory, Version=4.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Clients.ActiveDirectory.4.3.0\lib\net45\Microsoft.IdentityModel.Clients.ActiveDirectory.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Rest.ClientRuntime, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Rest.ClientRuntime.2.3.12\lib\net452\Microsoft.Rest.ClientRuntime.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Rest.ClientRuntime.Azure, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Rest.ClientRuntime.Azure.3.3.13\lib\net452\Microsoft.Rest.ClientRuntime.Azure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Types.14.0.1016.290\lib\net40\Microsoft.SqlServer.Types.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=9.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAzure.Storage.9.0.0\lib\net45\Microsoft.WindowsAzure.Storage.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework, Version=3.12.0.0, Culture=neutral, PublicKeyToken=2638cd05610744eb, processorArchitecture=MSIL">
      <HintPath>..\packages\NUnit.3.12.0\lib\net45\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="OpenPop, Version=2.0.6.2, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\OpenPop.2.0.6.2\lib\net20\OpenPop.dll</HintPath>
    </Reference>
    <Reference Include="Polly, Version=7.0.0.0, Culture=neutral, PublicKeyToken=c8a3ffc3f8f825cc, processorArchitecture=MSIL">
      <HintPath>..\packages\Polly.7.1.0\lib\netstandard2.0\Polly.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.Events.Core, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.Events.Core.1.0.314\lib\netstandard2.0\RoofSnap.Events.Core.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.2\lib\net46\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Runtime" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net461\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.1.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.4.0\lib\netstandard2.0\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.4.0\lib\net461\System.ValueTuple.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Http, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Tavis.UriTemplates, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Tavis.UriTemplates.1.1.1\lib\Net45\Tavis.UriTemplates.dll</HintPath>
    </Reference>
    <Reference Include="TechTalk.SpecFlow, Version=3.0.0.0, Culture=neutral, PublicKeyToken=0778194805d6db41, processorArchitecture=MSIL">
      <HintPath>..\packages\SpecFlow.3.0.225\lib\net45\TechTalk.SpecFlow.dll</HintPath>
    </Reference>
    <Reference Include="TechTalk.SpecFlow.NUnit.SpecFlowPlugin, Version=3.0.0.0, Culture=neutral, PublicKeyToken=0778194805d6db41, processorArchitecture=MSIL">
      <HintPath>..\packages\SpecFlow.NUnit.3.0.225\lib\net45\TechTalk.SpecFlow.NUnit.SpecFlowPlugin.dll</HintPath>
    </Reference>
    <Reference Include="Utf8Json, Version=1.3.7.0, Culture=neutral, PublicKeyToken=8a73d3ba7e392e27, processorArchitecture=MSIL">
      <HintPath>..\packages\Utf8Json.1.3.7\lib\net45\Utf8Json.dll</HintPath>
    </Reference>
    <Reference Include="WebMatrix.Data, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.Data.3.2.3\lib\net45\WebMatrix.Data.dll</HintPath>
    </Reference>
    <Reference Include="WebMatrix.WebData, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.WebData.3.2.3\lib\net45\WebMatrix.WebData.dll</HintPath>
    </Reference>
  </ItemGroup>
  <Choose>
    <When Condition="('$(VisualStudioVersion)' == '10.0' or '$(VisualStudioVersion)' == '') and '$(TargetFrameworkVersion)' == 'v3.5'">
      <ItemGroup>
        <Reference Include="Microsoft.VisualStudio.QualityTools.UnitTestFramework, Version=10.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
      </ItemGroup>
    </When>
    <Otherwise />
  </Choose>
  <ItemGroup>
    <Compile Include="Api\BraintreeWebhookFormData.cs" />
    <Compile Include="Api\DbFace.cs" />
    <Compile Include="Api\Dtos\ClonedProjectDto.cs" />
    <Compile Include="Api\Dtos\CompletedProfileDto.cs" />
    <Compile Include="Api\Dtos\CreateEstimateItemDto.cs" />
    <Compile Include="Api\Dtos\CreateEstimateTemplateDto.cs" />
    <Compile Include="Api\Dtos\CreateMeasurementOrderDto.cs" />
    <Compile Include="Api\Dtos\CreateSketchOrderDto.cs" />
    <Compile Include="Api\Dtos\CreateUserProfileDto.cs" />
    <Compile Include="Api\Dtos\DbItemType.cs" />
    <Compile Include="Api\Dtos\DbUnitType.cs" />
    <Compile Include="Api\Dtos\NotificationTemplateDto.cs" />
    <Compile Include="Api\Dtos\OrganizationFeatureDto.cs" />
    <Compile Include="Api\Dtos\ProjectImageDto.cs" />
    <Compile Include="Api\Dtos\ProjectShareDto.cs" />
    <Compile Include="Api\Dtos\SignUpSketchOrderDto.cs" />
    <Compile Include="Api\Dtos\SignUpDto.cs" />
    <Compile Include="Api\Dtos\SketchReportTypeDto.cs" />
    <Compile Include="Api\Dtos\UpdateEstimateDto.cs" />
    <Compile Include="Api\Dtos\UpdateEstimateItemDto.cs" />
    <Compile Include="Api\Dtos\UpdatePaymentMethodDto.cs" />
    <Compile Include="Api\Dtos\UpdateProjectOwnerDto.cs" />
    <Compile Include="Api\Dtos\V2Dtos\CreateDocumentV2Dto.cs" />
    <Compile Include="Api\Dtos\V2Dtos\CreateExternalImplementerSketchReportDto.cs" />
    <Compile Include="Api\Dtos\V2Dtos\ProjectImageV2Dto.cs" />
    <Compile Include="Api\Dtos\V2Dtos\SketchOptions.cs" />
    <Compile Include="Api\Dtos\V2Dtos\CreateSketchOrderV2Dto.cs" />
    <Compile Include="Api\Dtos\V2Dtos\IUpdateSketchOrderv2Dto.cs" />
    <Compile Include="Api\Dtos\V2Dtos\NotificationAcknowledgementDto.cs" />
    <Compile Include="Api\Dtos\V2Dtos\NotificationDto.cs" />
    <Compile Include="Api\Dtos\V2Dtos\SketchOrderDeliveryOptions.cs" />
    <Compile Include="Api\Dtos\V2Dtos\SketchOrderPaymentType.cs" />
    <Compile Include="Api\Dtos\V2Dtos\UpdateDocumentV2FormDataDto.cs" />
    <Compile Include="Api\Dtos\V2Dtos\UpdateSketchAdminSketchOrderV2Dto.cs" />
    <Compile Include="Api\Dtos\V2Dtos\UpdateSketchTechSketchOrderV2Dto.cs" />
    <Compile Include="Api\Dtos\UpdateSketchOrderDto.cs" />
    <Compile Include="Api\Dtos\V2Dtos\UpdateCustomerSketchOrderV2Dto.cs" />
    <Compile Include="Api\Dtos\UpdateUserProfileDto.cs" />
    <Compile Include="Api\ExportCSVModel.cs" />
    <Compile Include="Api\ExternalSharedAccessSignatureToken.cs" />
    <Compile Include="Api\IRoofSnapJobsApi.cs" />
    <Compile Include="Api\NotificationPriorityLevel.cs" />
    <Compile Include="Api\NotificationType.cs" />
    <Compile Include="Api\OrganizationFeature.cs" />
    <Compile Include="Api\OrganizationFeatureType.cs" />
    <Compile Include="Api\ProjectDocumentCategory.cs" />
    <Compile Include="Api\RoofSnapHttpClient.cs" />
    <Compile Include="Api\RoofSnapPaymentApi.cs" />
    <Compile Include="Api\RoofSnapServices.cs" />
    <Compile Include="Api\SketchOrderRefund.cs" />
    <Compile Include="Api\UpdatePaymentErrorModel.cs" />
    <Compile Include="Api\UpdatePaymentErrorType.cs" />
    <Compile Include="Api\UpdatePaymentMethodResult.cs" />
    <Compile Include="Api\WebHookEventType.cs" />
    <Compile Include="Api\WebHookSubscriptionDto.cs" />
    <Compile Include="EmailChecker\EmailChecker.cs" />
    <Compile Include="PropertyChecker\AssertableProperty.cs" />
    <Compile Include="PropertyChecker\ConventionsPropertyChecker.cs" />
    <Compile Include="Auth\AuthTests.cs" />
    <Compile Include="PropertyChecker\CustomPropertyChecker.cs" />
    <Compile Include="PropertyChecker\DateTimeOffsetPropertyChecker.cs" />
    <Compile Include="PropertyChecker\DefaultPropertyChecker.cs" />
    <Compile Include="PropertyChecker\ICustomPropertyChecker.cs" />
    <Compile Include="PropertyChecker\IPropertyChecker.cs" />
    <Compile Include="PasswordValidator.cs" />
    <Compile Include="PropertyChecker\NullPropertyChecker.cs" />
    <Compile Include="RunTestsInParallel.cs" />
    <Compile Include="SketchOrderTierSmokeTests.cs" />
    <Compile Include="SqlServerTypes\Loader.cs" />
    <Compile Include="StepDefinitions\DocumentSteps\V2\DocumentV2GivenSteps.cs" />
    <Compile Include="StepDefinitions\DocumentSteps\V2\DocumentV2ThenSteps.cs" />
    <Compile Include="StepDefinitions\DocumentSteps\V2\DocumentV2WhenSteps.cs" />
    <Compile Include="StepDefinitions\MeasurementOrderSteps\MeasurementOrderGivenSteps.cs" />
    <Compile Include="StepDefinitions\MeasurementOrderSteps\MeasurementOrderThenSteps.cs" />
    <Compile Include="StepDefinitions\MeasurementOrderSteps\MeasurementOrderWhenSteps.cs" />
    <Compile Include="StepDefinitions\SignUpSteps\SignUpWithSketchOrderSteps.cs" />
    <Compile Include="StepDefinitions\NotificationTemplateSteps\NotificationTemplateGivenSteps.cs" />
    <Compile Include="StepDefinitions\NotificationTemplateSteps\NotificationTemplateThenSteps.cs" />
    <Compile Include="StepDefinitions\NotificationTemplateSteps\NotificationTemplateWhenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectImageSteps\ProjectImageGivenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectImageSteps\ProjectImageThenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectImageSteps\ProjectImageWhenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectImageSteps\RoofImageGivenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectImageSteps\RoofImageThenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectImageSteps\RoofImageWhenSteps.cs" />
    <Compile Include="StepDefinitions\ReceiptsSteps\ReceiptsWhenSteps.cs" />
    <Compile Include="StepDefinitions\ReceiptsSteps\ReceiptsThenSteps.cs" />
    <Compile Include="StepDefinitions\SignUpSteps\SignUpGivenSteps.cs" />
    <Compile Include="StepDefinitions\SignUpSteps\SignUpThenSteps.cs" />
    <Compile Include="StepDefinitions\SignUpSteps\SignUpWhenSteps.cs" />
    <Compile Include="StepDefinitions\SketchOrderSteps\SketchOrderBillingSteps\SketchOrderBillingGivenSteps.cs" />
    <Compile Include="StepDefinitions\SketchOrderSteps\SketchOrderBillingSteps\SketchOrderBillingThenSteps.cs" />
    <Compile Include="StepDefinitions\SketchOrderSteps\SketchOrderBillingSteps\SketchOrderBillingWhenSteps.cs" />
    <Compile Include="StepDefinitions\SketchOrderSteps\SketchOrderV2Steps\SketchOrderV2GivenSteps.cs" />
    <Compile Include="StepDefinitions\SketchOrderSteps\SketchOrderV2Steps\SketchOrderV2ThenSteps.cs" />
    <Compile Include="StepDefinitions\SketchOrderSteps\SketchOrderV2Steps\SketchOrderV2WhenSteps.cs" />
    <Compile Include="StepDefinitions\UserProfileBillingSteps\UserProfileBillingGivenSteps.cs" />
    <Compile Include="StepDefinitions\UserProfileBillingSteps\UserProfileBillingThenSteps.cs" />
    <Compile Include="StepDefinitions\UserProfileBillingSteps\UserProfileBillingWhenSteps.cs" />
    <Compile Include="StepDefinitions\WebHookSubscriptionsSteps\WebHookSubscriptionGivenSteps.cs" />
    <Compile Include="StepDefinitions\WebHookSubscriptionsSteps\WebHookSubscriptionThenSteps.cs" />
    <Compile Include="StepDefinitions\WebHookSubscriptionsSteps\WebHookSubscriptionWhenSteps.cs" />
    <Compile Include="TestDataNext\DocumentV2Builder.cs" />
    <Compile Include="TestDataNext\DocumentV2RenderingsBuilder.cs" />
    <Compile Include="TestDataNext\IBuilder.cs" />
    <Compile Include="TestDataNext\NotificationAcknowledgementBuilder.cs" />
    <Compile Include="TestDataNext\NotificationTemplateBuilder.cs" />
    <Compile Include="TestDataNext\OfficeBuilder.cs" />
    <Compile Include="TestDataNext\OrganizationBuilder.cs" />
    <Compile Include="TestDataNext\OrganizationFeatureBuilder.cs" />
    <Compile Include="TestDataNext\OrganizationFreeSketchOrderBalanceBuilder.cs" />
    <Compile Include="TestDataNext\OrganizationFreeSketchOrderRateBuilder.cs" />
    <Compile Include="TestDataNext\ProjectBuilder.cs" />
    <Compile Include="TestDataNext\ProjectDocumentBuilder.cs" />
    <Compile Include="TestDataNext\ProjectDrawingBuilder.cs" />
    <Compile Include="TestDataNext\SketchOrderBillingTransactionBuilder.cs" />
    <Compile Include="TestDataNext\SketchOrderBuilder.cs" />
    <Compile Include="TestDataNext\NotificationBuilder.cs" />
    <Compile Include="TestDataNext\DocumentTemplateBuilder.cs" />
    <Compile Include="TestDataNext\TemplateCategoryBuilder.cs" />
    <Compile Include="TestDataNext\UserProfileBuilder.cs" />
    <Compile Include="TestDataNext\WebHookSubscriptionBuilder.cs" />
    <Compile Include="TestData\Braintree\BraintreeCustomerTestData.cs" />
    <Compile Include="TestData\Braintree\BraintreeGatewayFactory.cs" />
    <Compile Include="TestData\Braintree\BraintreeSubscriptionTestData.cs" />
    <Compile Include="TestData\Braintree\BraintreeTestData.cs" />
    <Compile Include="TestData\Builders\SketchOrderApiUriBuilderBase.cs" />
    <Compile Include="TestData\Builders\SharedProjectBuilder.cs" />
    <Compile Include="TestData\EstimateTemplateTestData.cs" />
    <Compile Include="TestData\FlagConverter.cs" />
    <Compile Include="TestData\Interfaces\IEstimateTemplateTestData.cs" />
    <Compile Include="TestData\Interfaces\IOfficeTestData.cs" />
    <Compile Include="TestData\OrganizationCreditTestData.cs" />
    <Compile Include="TestData\MeasurementsClassification.cs" />
    <Compile Include="TestData\OrganizationSettingType.cs" />
    <Compile Include="StepDefinitions\AppVersionStatusSteps\AppVersionCleanupSteps.cs" />
    <Compile Include="StepDefinitions\AppVersionStatusSteps\AppVersionStatusGivenSteps.cs" />
    <Compile Include="StepDefinitions\AppVersionStatusSteps\AppVersionStatusThenSteps.cs" />
    <Compile Include="StepDefinitions\AppVersionStatusSteps\AppVersionStatusWhenSteps.cs" />
    <Compile Include="StepDefinitions\DocumentSteps\DocumentGivenSteps.cs" />
    <Compile Include="StepDefinitions\DocumentTemplate\DocumentTemplateGivenSteps.cs" />
    <Compile Include="StepDefinitions\EstimateTemplate\EstimateTemplateGivenSteps.cs" />
    <Compile Include="StepDefinitions\EstimateTemplate\EstimateTemplateThenSteps.cs" />
    <Compile Include="StepDefinitions\EstimateTemplate\EstimateTemplateWhenSteps.cs" />
    <Compile Include="StepDefinitions\NearmapOrderSteps\NearmapOrderGivenSteps.cs" />
    <Compile Include="StepDefinitions\NearmapOrderSteps\NearmapOrderThenSteps.cs" />
    <Compile Include="StepDefinitions\NearmapOrderSteps\NearmapOrderWhenSteps.cs" />
    <Compile Include="StepDefinitions\NearmapTokenSteps\NearmapTokenGivenSteps.cs" />
    <Compile Include="StepDefinitions\NearmapTokenSteps\NearmapTokenThenSteps.cs" />
    <Compile Include="StepDefinitions\NearmapTokenSteps\NearmapTokenWhenSteps.cs" />
    <Compile Include="StepDefinitions\OfficeSteps\OfficeGivenSteps.cs" />
    <Compile Include="StepDefinitions\OfficeSteps\OfficeThenSteps.cs" />
    <Compile Include="StepDefinitions\OfficeSteps\OfficeWhenSteps.cs" />
    <Compile Include="StepDefinitions\OrganizationSteps\OrganizationGivenSteps.cs" />
    <Compile Include="StepDefinitions\OrganizationSteps\OrganizationThenSteps.cs" />
    <Compile Include="StepDefinitions\OrganizationSteps\OrganizationWhenSteps.cs" />
    <Compile Include="StepDefinitions\OrganizationSteps\ResultsWrapper.cs" />
    <Compile Include="StepDefinitions\ProjectDocumentSteps\ProjectDocumentGivenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectDocumentSteps\ProjectDocumentThenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectDocumentSteps\ProjectDocumentWhenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectDrawingSteps\ProjectDrawingGivenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectDrawingSteps\ProjectDrawingThenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectDrawingSteps\ProjectDrawingWhenSteps.cs" />
    <Compile Include="StepDefinitions\RoofSnapGivenSteps.cs" />
    <Compile Include="StepDefinitions\RoofSnapThenSteps.cs" />
    <Compile Include="StepDefinitions\RoofSnapWhenSteps.cs" />
    <Compile Include="StepDefinitions\SketchOrderSteps\SketchOrderGivenSteps.cs" />
    <Compile Include="StepDefinitions\SketchOrderSteps\SketchOrderThenSteps.cs" />
    <Compile Include="StepDefinitions\SketchOrderSteps\SketchOrderWhenSteps.cs" />
    <Compile Include="TestData\Builders\Interface\ISketchOrderBuilder.cs" />
    <Compile Include="TestData\Builders\SketchOrderOrganizationBuilder.cs" />
    <Compile Include="TestData\Builders\SketchOrderOrganizationQueryBuilder.cs" />
    <Compile Include="TestData\Builders\SketchOrdersByOrganizationApiUriBuilder.cs" />
    <Compile Include="TestData\Builders\SketchOrdersByRequestedOrOwnerApiUriBuilder.cs" />
    <Compile Include="TestData\Builders\SketchOrderRequestedOrOwnerBuilder.cs" />
    <Compile Include="TestData\Builders\SketchOrderRequestedOrOwnerQueryBuilder.cs" />
    <Compile Include="StepDefinitions\DocumentTemplate\DocumentTemplateThenSteps.cs" />
    <Compile Include="StepDefinitions\DocumentTemplate\DocumentTemplateWhenSteps.cs" />
    <Compile Include="StepDefinitions\JobStepDefinitions\JobGivenSteps.cs" />
    <Compile Include="StepDefinitions\JobStepDefinitions\JobThenSteps.cs" />
    <Compile Include="StepDefinitions\JobStepDefinitions\JobWhenSteps.cs" />
    <Compile Include="TestData\ProjectImageTestData.cs" />
    <Compile Include="TestData\RoofSnapRoles.cs" />
    <Compile Include="TestData\TestDataFactories\ProjectDrawingTestDataFactory.cs" />
    <Compile Include="TestData\TestDataFactories\SharedProjectTestDataFactory.cs" />
    <Compile Include="TestData\TestDataGenerators\ProjectDrawingTestDataBuilder.cs" />
    <Compile Include="TestData\TestDataGenerators\ProjectEstimateItemTestData.cs" />
    <Compile Include="TestData\UserTestData.cs" />
    <Compile Include="TestData\TestDataFactories\GeographicPositionTestDataFactory.cs" />
    <Compile Include="TestData\TestDataFactories\OfficeTestDataFactory.cs" />
    <Compile Include="TestData\TestDataFactories\OrganizationSettingsTestDataFactory.cs" />
    <Compile Include="TestData\TestDataFactories\OrganizationTestDataFactory.cs" />
    <Compile Include="TestData\TestDataFactories\ProjectDocumentTestDataFactory.cs" />
    <Compile Include="TestData\TestDataFactories\UserOfficeTestDataFactory.cs" />
    <Compile Include="TestData\TestDataFactories\UserProfileTestDataFactory.cs" />
    <Compile Include="TestData\TestDataFactories\WebpagesMembershipTestDataFactory.cs" />
    <Compile Include="TestSetup\Configuration.cs" />
    <Compile Include="Api\Dtos\CreateEstimateDto.cs" />
    <Compile Include="Api\Dtos\ItemType.cs" />
    <Compile Include="Api\Dtos\ResetPasswordDto.cs" />
    <Compile Include="Api\Dtos\SketchReportPinsImageDto.cs" />
    <Compile Include="Api\Dtos\UnitType.cs" />
    <Compile Include="Api\JobResultRepresentation.cs" />
    <Compile Include="TestData\OfficeDocumentTestData.cs" />
    <Compile Include="TestData\OfficeTestData.cs" />
    <Compile Include="TestData\DocumentTestData.cs" />
    <Compile Include="TestData\DocumentTemplateTestData.cs" />
    <Compile Include="Api\Dtos\AreaImageDto.cs" />
    <Compile Include="Api\Dtos\CreateDocumentDto.cs" />
    <Compile Include="Api\Dtos\CreateDocumentTemplateDto.cs" />
    <Compile Include="Api\Dtos\MeasurementsImageDto.cs" />
    <Compile Include="Api\Dtos\PitchImageDto.cs" />
    <Compile Include="Api\Dtos\UpdateDocumentDto.cs" />
    <Compile Include="Api\Dtos\UpdateDocumentTemplateDto.cs" />
    <Compile Include="Api\Dtos\UpdateMultiProjectOwnerDto.cs" />
    <Compile Include="TestData\ProjectEstimateItemBuilder.cs" />
    <Compile Include="TestData\ProjectEstimateOptionBuilder.cs" />
    <Compile Include="StepDefinitions\DocumentSteps\DocumentThenSteps.cs" />
    <Compile Include="StepDefinitions\DocumentSteps\DocumentWhenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectSteps\ProjectGivenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectSteps\ProjectThenSteps.cs" />
    <Compile Include="StepDefinitions\ProjectSteps\ProjectWhenSteps.cs" />
    <Compile Include="StepDefinitions\NearmapConfigSteps\NearmapConfigThenSteps.cs" />
    <Compile Include="StepDefinitions\NearmapConfigSteps\NearmapConfigWhenSteps.cs" />
    <Compile Include="StepDefinitions\UserProfileSteps\UserProfileThenSteps.cs" />
    <Compile Include="StepDefinitions\UserProfileSteps\UserProfileWhenSteps.cs" />
    <Compile Include="StepDefinitions\UserRoleSteps\UserRolesWhenSteps.cs" />
    <Compile Include="StepDefinitions\UserRoleSteps\UserRolesGivenSteps.cs" />
    <Compile Include="TestData\NearmapTestData.cs" />
    <Compile Include="Api\Dtos\AppVersionStatusDto.cs" />
    <Compile Include="Api\Dtos\Coordinate.cs" />
    <Compile Include="Api\Dtos\CreateProjectDto.cs" />
    <Compile Include="Api\Dtos\EdgesDto.cs" />
    <Compile Include="Api\Dtos\EdgeSubTypeDto.cs" />
    <Compile Include="Api\Dtos\EdgeTypeDto.cs" />
    <Compile Include="Api\Dtos\FacesDto.cs" />
    <Compile Include="Api\Dtos\FaceTypeDto.cs" />
    <Compile Include="Api\Dtos\MeasurementsDto.cs" />
    <Compile Include="Api\Dtos\NearmapOrderDto.cs" />
    <Compile Include="Api\Dtos\PinDto.cs" />
    <Compile Include="Api\Dtos\ProjectDocumentDto.cs" />
    <Compile Include="Api\Dtos\ProjectDrawingDto.cs" />
    <Compile Include="Api\Dtos\RoofImageDto.cs" />
    <Compile Include="Api\Dtos\ProjectStatus.cs" />
    <Compile Include="Api\Dtos\UpdateProjectDto.cs" />
    <Compile Include="Api\Dtos\UpdateProjectMapDto.cs" />
    <Compile Include="Api\Dtos\VertexesDto.cs" />
    <Compile Include="Api\JwtToken.cs" />
    <Compile Include="TestData\TestDataGenerators\ProjectDrawingDtoTestDataGenerator.cs" />
    <Compile Include="TestData\TestDataGenerators\ProjectDrawingEdgesTestData.cs" />
    <Compile Include="TestData\TestDataGenerators\ProjectDrawingFacesTestData.cs" />
    <Compile Include="TestData\TestDataGenerators\ProjectDrawingPinsTestData.cs" />
    <Compile Include="TestData\TestDataGenerators\ProjectDrawingVertexesTestData.cs" />
    <Compile Include="TestData\TestDataGenerators\UpdateProjectDrawingTestData.cs" />
    <Compile Include="TestData\TestDataGenerators\ProjectMeasurementsTestDataGenerator.cs" />
    <Compile Include="IReturnedResourceObjectChecker.cs" />
    <Compile Include="IoC.cs" />
    <Compile Include="TestData\ProjectTestData.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="ReturnedResourceObjectChecker.cs" />
    <Compile Include="Api\RoofSnapJobsApi.cs" />
    <Compile Include="ScenarioContextExtensions.cs" />
    <Compile Include="TestSetup\EndToEndTest.cs" />
    <Compile Include="TestSetup\IisExpressWebServer.cs" />
    <Compile Include="Api\Dtos\SketchOrderStatus.cs" />
    <Compile Include="TestData\SketchOrderTestData.cs" />
    <Compile Include="Api\Dtos\SketchReportType.cs" />
    <Compile Include="TestSetup\TestSetup.cs" />
    <Compile Include="StepDefinitions\UserProfileSteps\UserProfileGivenSteps.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Api\RoofSnapApi.cs" />
    <Compile Include="StepDefinitions\RoofSnapApiSteps.cs" />
    <Compile Include="Api\RoofSnapClientFactory.cs" />
    <Compile Include="StepDefinitions\SharedSteps.cs" />
    <Compile Include="TestSetup\WebApplication.cs" />
    <Content Include="EF.Reverse.POCO.ttinclude" />
    <Content Include="EF.Reverse.POCO.Core.ttinclude" />
    <Compile Include="roofsnaplive.cs">
      <DependentUpon>roofsnaplive.tt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Content Include="SqlServerTypes\readme.htm" />
    <Content Include="SqlServerTypes\x64\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x64\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Features\Documents\V2\CreateDocumentsV2.feature" />
    <None Include="Features\Documents\V2\CreateSaSToken.feature" />
    <None Include="Features\Documents\V2\DeleteDocumentsV2.feature" />
    <None Include="Features\Documents\V2\GetDocumentsV2.feature" />
    <None Include="Features\Documents\V2\UpdateDocumentV2.feature" />
    <None Include="Features\EstimateTemplates\CreateEstimateTemplate.feature" />
    <None Include="Features\EstimateTemplates\DeleteAnEstimateTemplate.feature" />
    <None Include="Features\EstimateTemplates\GetEstimateTemplates.feature" />
    <None Include="Features\AppVersionStatus\CheckAppVersionStatus.feature" />
    <None Include="Features\AppVersionStatus\CreateAppVersionStatus.feature" />
    <None Include="Features\AppVersionStatus\GetAppVersionStatuses.feature" />
    <None Include="Features\AppVersionStatus\UpdateAppVersionStatus.feature" />
    <None Include="Features\Documents\CreateDocument.feature" />
    <None Include="Features\Documents\GetDocuments.feature" />
    <None Include="Features\Documents\UpdateDocument.feature" />
    <None Include="Features\DocumentTemplates\CreateADocumentTemplate.feature" />
    <None Include="Features\DocumentTemplates\GetDocumentTemplates.feature" />
    <None Include="Features\DocumentTemplates\UpdateDocumentTemplate.feature" />
    <None Include="Features\Jobs\AutoCancelSubscriptionAfterUnusedTrial.feature" />
    <None Include="Features\Jobs\EnableAddOns.feature" />
    <None Include="Features\Jobs\DisableAddOns.feature" />
    <None Include="Features\Jobs\RunOrganizationSketchOrderBilling.feature" />
    <None Include="Features\Jobs\UpdateProjectOwner.feature" />
    <None Include="Features\MeasurementOrders\CreateMeasurementOrder.feature" />
    <None Include="Features\MeasurementOrders\GetMeasurementOrder.feature" />
    <None Include="Features\NearmapConfig\GetNearmapConfig.feature" />
    <None Include="Features\NearmapOrders\CreateNearmapOrder.feature" />
    <None Include="Features\NearmapOrders\GetNearmapOrders.feature" />
    <None Include="Features\NearmapTokens\GetNearmapToken.feature" />
    <None Include="Features\NotificationTemplates\CreateNotificationTemplate.feature" />
    <None Include="Features\NotificationTemplates\GetNotificationTemplate.feature" />
    <None Include="Features\NotificationTemplates\UpdateNotificationTemplate.feature" />
    <None Include="Features\Office\DeleteOfficeFeature.feature" />
    <None Include="Features\Office\GetColorOptionsByMaterialItemAndOffice.feature" />
    <None Include="Features\Office\GetEstimateRulesByOffice.feature" />
    <None Include="Features\Office\GetMaterialCategoriesByOffice.feature" />
    <None Include="Features\Office\GetMaterialItemsByOffice.feature" />
    <None Include="Features\Office\GetDocumentTemplateCategoriesByOffice.feature" />
    <None Include="Features\Office\GetOfficeDocumentsByOffice.feature" />
    <None Include="Features\Office\OfficeContractTerms.feature" />
    <None Include="Features\Office\GetOffices.feature" />
    <None Include="Features\Office\GetUserProfilesInAnOffice.feature" />
    <None Include="Features\Office\SearchMaterialByOffice.feature" />
    <None Include="Features\Organization\GetAccountSubscriptionByOrganizationAndSubscription.feature" />
    <None Include="Features\Organization\GetDocumentTemplatesByOrganization.feature" />
    <None Include="Features\Organization\GetFeaturesByOrganization.feature" />
    <None Include="Features\Organization\GetEstimateTemplatesByOrganization.feature" />
    <None Include="Features\Organization\GetOfficesByOrganization.feature" />
    <None Include="Features\Organization\GetOrganizationsFreeSketchOrderBalance.feature" />
    <None Include="Features\Organization\GetOrganizationsFeature.feature" />
    <None Include="Features\Organization\GetOrganizationsFreeSketchOrderRate.feature" />
    <None Include="Features\Organization\GetPricingByOrganization.feature" />
    <None Include="Features\Organization\GetProjectsByOrganization.feature" />
    <None Include="Features\NearmapTokens\CreateNearmapToken.feature" />
    <None Include="Features\Organization\UpdateOrganizationFeatures.feature" />
    <None Include="Features\Organization\UpdateOrganizationFreeSketchOrderRate.feature" />
    <None Include="Features\Organization\UpdateOrganizationsFreeSketchOrderBalance.feature" />
    <None Include="Features\ProjectDocuments\CreateProjectDocument.feature" />
    <None Include="Features\ProjectDocuments\DeleteProjectDocument.feature" />
    <None Include="Features\ProjectDocuments\GetProjectDocuments.feature" />
    <None Include="Features\ProjectDrawings\ProjectDrawing.feature" />
    <None Include="Features\ProjectImages\CreateProjectImage.feature" />
    <None Include="Features\ProjectImages\GetProjectImages.feature" />
    <None Include="Features\ProjectImages\UpdateRoofImage.feature" />
    <None Include="Features\Projects\RestoreProject.feature" />
    <None Include="Features\Projects\CloneProject.feature" />
    <None Include="Features\Projects\CreateProjectDocumentImages.feature" />
    <None Include="Features\Projects\EstimateItemsRoundToOrderableQuantity.feature" />
    <None Include="Features\Projects\CreateProjectEstimateItemByEstimate.feature" />
    <None Include="Features\Projects\CreateProjectEstimateItemByEstimateCalculatesTotals.feature" />
    <None Include="Features\Projects\CreateProjectShare.feature" />
    <None Include="Features\Projects\DeleteProject.feature" />
    <None Include="Features\Projects\DeleteProjectEstimate.feature" />
    <None Include="Features\Projects\DeleteProjectEstimateItem.feature" />
    <None Include="Features\Projects\DeleteProjectEstimateItemCalculatesTotals.feature" />
    <None Include="Features\Projects\DeleteProjectShare.feature" />
    <None Include="Features\Projects\ExportProjectCSV.feature" />
    <None Include="Features\Projects\GenerateProjectDocuments.feature" />
    <None Include="Features\Projects\GetDocumentsByProject.feature" />
    <None Include="Features\Projects\GetMeasurementOrdersByProject.feature" />
    <None Include="Features\Projects\GetOrganizationByProject.feature" />
    <None Include="Features\Projects\GetProjectDocuments.feature" />
    <None Include="Features\Projects\GetProjectEstimateItems.feature" />
    <None Include="Features\Projects\GetProjectEstimateItemsByEstimateId.feature" />
    <None Include="Features\Projects\GetProjectEstimates.feature" />
    <None Include="Features\Projects\GetProjectImages.feature" />
    <None Include="Features\Projects\GetProjects.feature" />
    <None Include="Features\Projects\CreateProject.feature" />
    <None Include="Features\Projects\CreateProjectEstimate.feature" />
    <None Include="Features\Projects\GetProjectShare.feature" />
    <None Include="Features\Projects\GetSketchOrdersByProject.feature" />
    <None Include="Features\Projects\UpdateProject.feature" />
    <None Include="Features\Projects\UpdateProjectEstimate.feature" />
    <None Include="Features\Projects\UpdateProjectEstimateItem.feature" />
    <None Include="Features\Projects\UpdateProjectImage.feature" />
    <None Include="Features\Receipts\Receipts.feature" />
    <None Include="Features\SignUp\NewSignUpWithSketchOS.feature" />
    <None Include="Features\SignUp\NewSignUp.feature" />
    <None Include="Features\SketchOrder\CalculateSketchOrderPricingOverrideFullSnap.feature" />
    <None Include="Features\SketchOrder\CalculateSketchOrderPricingHalfSnap.feature" />
    <None Include="Features\SketchOrder\CalculateSketchOrderPricingFullSnap.feature" />
    <None Include="Features\SketchOrder\CalculateSketchOrderPricingOverrideHalfSnap.feature" />
    <None Include="Features\SketchOrder\CompleteSketchOrder.feature" />
    <None Include="Features\SketchOrder\CreateSketchOrder.feature" />
    <None Include="Features\SketchOrder\GetSketchOrdersByOwnerOrRequester.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\CreateExternalImplementerSketchReport.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\CreateSketchOrderV2.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\DeleteProjectFromSketchOrder.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\SketchOrderDeliveryNotifications\CreateSketchOrderDeliveryNotificationAcknowledgement.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\SketchOrderDeliveryNotifications\DeleteSketchOrderDeliveryNotification.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\SketchOrderDeliveryNotifications\DeleteSketchOrderDeliveryNotificationAcknowledgement.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\SketchOrderDeliveryNotifications\GetSketchOrderDeliveryNotificationAcknowledgement.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\GetSketchOrderV2.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\BillingSketchOrders.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\SketchOrderDeliveryNotifications\CreateSketchOrderDeliveryNotification.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\SketchOrderDeliveryNotifications\GetSketchOrderDeliveryNotifications.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\SketchOrderDeliveryNotifications\UpdateSketchOrderDeliveryNotification.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\SketchOrderEmailDelivery.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\StatusTransitionTests\StatusTransitionBilledTests.feature">
      <LastGenOutput>StatusTransitionBilledTests.cs</LastGenOutput>
    </None>
    <None Include="Features\SketchOrder\SketchOrderV2\StatusTransitionTests\StatusTransitionCancelledTests.feature">
      <LastGenOutput>StatusTransitionCancelledTests.cs</LastGenOutput>
    </None>
    <None Include="Features\SketchOrder\SketchOrderV2\StatusTransitionTests\StatusTransitionCompleteTests.feature">
      <LastGenOutput>StatusTransitionCompleteTests.cs</LastGenOutput>
    </None>
    <None Include="Features\SketchOrder\SketchOrderV2\StatusTransitionTests\StatusTransitionExceptionTests.feature">
      <LastGenOutput>StatusTransitionExceptionTests.cs</LastGenOutput>
    </None>
    <None Include="Features\SketchOrder\SketchOrderV2\StatusTransitionTests\StatusTransitionIncompleteTests.feature">
      <LastGenOutput>StatusTransitionIncompleteTests.cs</LastGenOutput>
    </None>
    <None Include="Features\SketchOrder\SketchOrderV2\StatusTransitionTests\StatusTransitionInProgressTests.feature">
      <LastGenOutput>StatusTransitionInProgressTests.cs</LastGenOutput>
    </None>
    <None Include="Features\SketchOrder\SketchOrderV2\StatusTransitionTests\StatusTransitionPendingRejectionTests.feature">
      <LastGenOutput>StatusTransitionPendingRejectionTests.cs</LastGenOutput>
    </None>
    <None Include="Features\SketchOrder\SketchOrderV2\StatusTransitionTests\StatusTransitionPendingReviewTests.feature">
      <LastGenOutput>StatusTransitionPendingReviewTests .cs</LastGenOutput>
    </None>
    <None Include="Features\SketchOrder\SketchOrderV2\StatusTransitionTests\StatusTransitionPendingTests.feature">
      <LastGenOutput>StatusTransitionPendingTests.cs</LastGenOutput>
    </None>
    <None Include="Features\SketchOrder\SketchOrderV2\StatusTransitionTests\StatusTransitionInReviewTests.feature">
      <LastGenOutput>StatusTransitionInReviewTests.cs</LastGenOutput>
    </None>
    <None Include="Features\SketchOrder\SketchOrderV2\StatusTransitionTests\StatusTransitionBillingFailedTests.feature">
      <LastGenOutput>StatusTransitionBillingFailedTests.cs</LastGenOutput>
    </None>
    <None Include="Features\SketchOrder\SketchOrderV2\StatusTransitionTests\StatusTransitionQueuedTests.feature">
      <LastGenOutput>StatusTransitionQueuedTests.cs</LastGenOutput>
    </None>
    <None Include="Features\SketchOrder\SketchOrderV2\UpdateSketchOrderPayment.feature" />
    <None Include="Features\SketchOrder\SketchOrderV2\UpdateSketchOrderV2.feature" />
    <None Include="Features\SketchOrder\UpdateSketchOrder.feature" />
    <None Include="Features\UserProfileBilling\UpdateUserPaymentMethod.feature" />
    <None Include="Features\UserProfile\CreateUserProfile.feature" />
    <None Include="Features\UserProfile\DeleteUserOffice.feature" />
    <None Include="Features\UserProfile\GetAssignedSketchOrdersBySketchTech.feature" />
    <None Include="Features\Organization\GetOrganizations.feature" />
    <None Include="Features\UserProfile\GetMeasurementOrdersByUserProfile.feature" />
    <None Include="Features\UserProfile\GetOfficeDocumentsByUserProfile.feature" />
    <None Include="Features\UserProfile\GetOfficesByUserProfile.feature" />
    <None Include="Features\UserProfile\GetOrganizationByUserProfile.feature" />
    <None Include="Features\SketchOrder\GetSketchOrdersByOwner.feature" />
    <None Include="Features\SketchOrder\GetSketchOrdersByRequester.feature" />
    <None Include="Features\Organization\GetSketchOrdersByOrganization.feature" />
    <None Include="Features\SketchOrder\GetOrganizationBySketchOrder.feature" />
    <None Include="Features\SketchOrder\GetOwnerBySketchOrder.feature" />
    <None Include="Features\SketchOrder\GetSketchOrders.feature" />
    <None Include="Features\SketchOrder\GetSketchOrderRequester.feature" />
    <None Include="Features\SketchOrder\GetSketchTechBySketchOrder.feature" />
    <None Include="Features\UserProfile\GetProjectsByUserProfile.feature" />
    <None Include="Features\Organization\GetUserProfilesByOrganization.feature" />
    <None Include="Features\UserProfile\GetUserProfiles.feature" />
    <None Include="Features\UserProfile\ChangeUserProfilePassword.feature" />
    <None Include="Features\Projects\GiveAProjectToADifferentUser.feature" />
    <None Include="Features\UserProfile\UpdateUserProfile.feature" />
    <None Include="Features\WebhookSubscriptions\CreateWebHookSubscription.feature" />
    <None Include="Features\WebhookSubscriptions\DeleteWebHookSubscription.feature" />
    <None Include="Features\WebhookSubscriptions\GetWebHooksSubscriptions.feature" />
    <None Include="Features\WebhookSubscriptions\UpdateWebHookSubscription.feature" />
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Features\SketchOrder\StartSketchOrder.feature" />
    <None Include="Features\UserRole\GetUserRoles.feature" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\testimage.jpg" />
    <Content Include="FodyWeavers.xml" />
    <Content Include="roofsnaplive.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>roofsnaplive.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RoofSnap.WebAPI\RoofSnap.WebAPI.csproj">
      <Project>{82E93AC9-0506-40DE-ACA6-18B9FFAB9A48}</Project>
      <Name>RoofSnap.WebAPI</Name>
    </ProjectReference>
  </ItemGroup>
  <Choose>
    <When Condition="'$(VisualStudioVersion)' == '10.0' And '$(IsCodedUITest)' == 'True'">
      <ItemGroup>
        <Reference Include="Microsoft.VisualStudio.QualityTools.CodedUITestFramework, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <Private>False</Private>
        </Reference>
        <Reference Include="Microsoft.VisualStudio.TestTools.UITest.Common, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <Private>False</Private>
        </Reference>
        <Reference Include="Microsoft.VisualStudio.TestTools.UITest.Extension, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <Private>False</Private>
        </Reference>
        <Reference Include="Microsoft.VisualStudio.TestTools.UITesting, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <Private>False</Private>
        </Reference>
      </ItemGroup>
    </When>
  </Choose>
  <Import Project="$(VSToolsPath)\TeamTest\Microsoft.TestTools.targets" Condition="Exists('$(VSToolsPath)\TeamTest\Microsoft.TestTools.targets')" />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\NUnit.3.12.0\build\NUnit.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NUnit.3.12.0\build\NUnit.props'))" />
    <Error Condition="!Exists('..\packages\SpecFlow.NUnit.3.0.225\build\SpecFlow.NUnit.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\SpecFlow.NUnit.3.0.225\build\SpecFlow.NUnit.props'))" />
    <Error Condition="!Exists('..\packages\SpecFlow.NUnit.3.0.225\build\SpecFlow.NUnit.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\SpecFlow.NUnit.3.0.225\build\SpecFlow.NUnit.targets'))" />
    <Error Condition="!Exists('..\packages\SpecFlow.Tools.MsBuild.Generation.3.0.225\build\SpecFlow.Tools.MsBuild.Generation.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\SpecFlow.Tools.MsBuild.Generation.3.0.225\build\SpecFlow.Tools.MsBuild.Generation.props'))" />
    <Error Condition="!Exists('..\packages\SpecFlow.Tools.MsBuild.Generation.3.0.225\build\SpecFlow.Tools.MsBuild.Generation.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\SpecFlow.Tools.MsBuild.Generation.3.0.225\build\SpecFlow.Tools.MsBuild.Generation.targets'))" />
    <Error Condition="!Exists('..\packages\NUnit3TestAdapter.3.16.1\build\net35\NUnit3TestAdapter.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NUnit3TestAdapter.3.16.1\build\net35\NUnit3TestAdapter.props'))" />
  </Target>
  <Import Project="..\packages\SpecFlow.NUnit.3.0.225\build\SpecFlow.NUnit.targets" Condition="Exists('..\packages\SpecFlow.NUnit.3.0.225\build\SpecFlow.NUnit.targets')" />
  <Import Project="..\packages\SpecFlow.Tools.MsBuild.Generation.3.0.225\build\SpecFlow.Tools.MsBuild.Generation.targets" Condition="Exists('..\packages\SpecFlow.Tools.MsBuild.Generation.3.0.225\build\SpecFlow.Tools.MsBuild.Generation.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>