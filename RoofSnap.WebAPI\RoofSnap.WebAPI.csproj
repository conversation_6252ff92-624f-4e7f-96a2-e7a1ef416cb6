﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.Net.Compilers.3.3.1\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.3.3.1\build\Microsoft.Net.Compilers.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{82E93AC9-0506-40DE-ACA6-18B9FFAB9A48}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RoofSnap.WebAPI</RootNamespace>
    <AssemblyName>RoofSnap.WebAPI</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
    <AutoParameterizationWebConfigConnectionStrings>false</AutoParameterizationWebConfigConnectionStrings>
    <Use64BitIISExpress>
    </Use64BitIISExpress>
    <ApplicationInsightsResourceId>/subscriptions/c26b7d04-2e62-4211-805c-5852272d9204/resourcegroups/tfdev-east2/providers/microsoft.insights/components/tfdev-roofsnap-api-ai</ApplicationInsightsResourceId>
    <ApplicationInsightsAnnotationResourceId>/subscriptions/c26b7d04-2e62-4211-805c-5852272d9204/resourcegroups/tfdev-east2/providers/microsoft.insights/components/tfdev-roofsnap-api-ai</ApplicationInsightsAnnotationResourceId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\RoofSnap.WebAPI.XML</DocumentationFile>
    <CodeAnalysisRuleSet>RoofSnap.WebAPI.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\RoofSnap.WebAPI.XML</DocumentationFile>
    <CodeAnalysisRuleSet>RoofSnap.WebAPI.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ActionMailerNext, Version=3.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ActionMailerNext.3.2.0.0\lib\net45\ActionMailerNext.dll</HintPath>
    </Reference>
    <Reference Include="ActionMailerNext.Mvc5-2, Version=3.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ActionMailerNext.Mvc5-2.3.2.0.0\lib\net45\ActionMailerNext.Mvc5-2.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper, Version=5.2.0.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.5.2.0\lib\net45\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="Braintree, Version=4.8.0.0, Culture=neutral, PublicKeyToken=31b586f34d3e96c7, processorArchitecture=MSIL">
      <HintPath>..\packages\Braintree.4.8.0\lib\net452\Braintree.dll</HintPath>
    </Reference>
    <Reference Include="Castle.Core, Version=*******, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <HintPath>..\packages\Castle.Core.4.2.0\lib\net45\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="CsQuery, Version=1.3.3.249, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\CsQuery.1.3.4\lib\net40\CsQuery.dll</HintPath>
    </Reference>
    <Reference Include="DistributedLock, Version=1.4.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DistributedLock.1.4.0\lib\net45\DistributedLock.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="FastMember, Version=1.0.0.9, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\FastMember.1.0.0.11\lib\net40\FastMember.dll</HintPath>
    </Reference>
    <Reference Include="FileHelpers, Version=3.2.7.0, Culture=neutral, PublicKeyToken=3e0c08d59cc3d657, processorArchitecture=MSIL">
      <HintPath>..\packages\FileHelpers.3.2.7\lib\net45\FileHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Flurl, Version=2.5.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Flurl.2.5.0\lib\net40\Flurl.dll</HintPath>
    </Reference>
    <Reference Include="Flurl.Http, Version=2.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Flurl.Http.2.0.1\lib\net45\Flurl.Http.dll</HintPath>
    </Reference>
    <Reference Include="GeoAPI, Version=1.7.5.0, Culture=neutral, PublicKeyToken=a1a0da7def465678, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoAPI.Core.1.7.5\lib\net45\GeoAPI.dll</HintPath>
    </Reference>
    <Reference Include="GeoAPI.CoordinateSystems, Version=1.7.5.0, Culture=neutral, PublicKeyToken=a1a0da7def465678, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoAPI.CoordinateSystems.1.7.5\lib\net45\GeoAPI.CoordinateSystems.dll</HintPath>
    </Reference>
    <Reference Include="GeoJSON.Net, Version=0.1.51.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoJSON.Net.0.1.51\lib\portable-net40+sl5+wp80+win8+wpa81\GeoJSON.Net.dll</HintPath>
    </Reference>
    <Reference Include="GeoJSON.Net.Contrib.MsSqlSpatial, Version=0.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoJSON.Net.Contrib.MsSqlSpatial.0.2.0\lib\net45\GeoJSON.Net.Contrib.MsSqlSpatial.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Core, Version=1.32.2.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Core.1.32.2\lib\net45\Google.Apis.Core.dll</HintPath>
    </Reference>
    <Reference Include="Handlebars, Version=1.8.1.0, Culture=neutral, PublicKeyToken=22225d0bf33cd661, processorArchitecture=MSIL">
      <HintPath>..\packages\Handlebars.Net.1.9.0\lib\net40\Handlebars.dll</HintPath>
    </Reference>
    <Reference Include="HtmlAgilityPack, Version=1.4.9.5, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a, processorArchitecture=MSIL">
      <HintPath>..\packages\HtmlAgilityPack.1.4.9.5\lib\Net45\HtmlAgilityPack.dll</HintPath>
    </Reference>
    <Reference Include="IdentityModel, Version=2.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IdentityModel.2.0.1\lib\net45\IdentityModel.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp, Version=5.5.11.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.5.5.11\lib\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp.xmlworker, Version=5.5.11.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itextsharp.xmlworker.5.5.11\lib\itextsharp.xmlworker.dll</HintPath>
    </Reference>
    <Reference Include="LINQtoCSV, Version=1.5.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\LINQtoCSV.1.5.0.0\lib\net35\LINQtoCSV.dll</HintPath>
    </Reference>
    <Reference Include="Logzio.DotNet.Core, Version=1.0.6.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Logzio.DotNet.NLog.1.0.6\lib\net45\Logzio.DotNet.Core.dll</HintPath>
    </Reference>
    <Reference Include="Logzio.DotNet.NLog, Version=1.0.6.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Logzio.DotNet.NLog.1.0.6\lib\net45\Logzio.DotNet.NLog.dll</HintPath>
    </Reference>
    <Reference Include="Mandrill, Version=1.2.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Mandrill.1.2.1.0\lib\net40\Mandrill.dll</HintPath>
    </Reference>
    <Reference Include="Marvin.JsonPatch, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Marvin.JsonPatch.1.0.0\lib\portable40-net40+win8+wpa81\Marvin.JsonPatch.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Agent.Intercept, Version=2.4.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Agent.Intercept.2.4.0\lib\net45\Microsoft.AI.Agent.Intercept.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.DependencyCollector, Version=2.10.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.10.0\lib\net45\Microsoft.AI.DependencyCollector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.PerfCounterCollector, Version=2.10.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.10.0\lib\net45\Microsoft.AI.PerfCounterCollector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.ServerTelemetryChannel, Version=2.10.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.10.0\lib\net45\Microsoft.AI.ServerTelemetryChannel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Web, Version=2.10.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Web.2.10.0\lib\net45\Microsoft.AI.Web.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.WindowsServer, Version=2.10.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.2.10.0\lib\net45\Microsoft.AI.WindowsServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=2.10.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.10.0\lib\net46\Microsoft.ApplicationInsights.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights.NLogTarget, Version=2.10.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.NLogTarget.2.10.0\lib\net45\Microsoft.ApplicationInsights.NLogTarget.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.TelemetryCorrelation, Version=1.0.5.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.TelemetryCorrelation.1.0.5\lib\net45\Microsoft.AspNet.TelemetryCorrelation.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Authentication.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Authentication.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authentication.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Authentication.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Authentication.Core.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authentication.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Authorization, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Authorization.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authorization.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Authorization.Policy, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Authorization.Policy.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authorization.Policy.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Hosting.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Hosting.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Hosting.Server.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Hosting.Server.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Extensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Extensions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Features.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Features.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Mvc.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Mvc.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Mvc.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Mvc.Core, Version=2.2.5.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Mvc.Core.2.2.5\lib\netstandard2.0\Microsoft.AspNetCore.Mvc.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.ResponseCaching.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.ResponseCaching.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Routing, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Routing.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Routing.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Routing.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Routing.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Routing.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.WebUtilities, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.WebUtilities.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.WebUtilities.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.EventGrid, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.EventGrid.2.0.0\lib\net452\Microsoft.Azure.EventGrid.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.KeyVault.Core.1.0.0\lib\net40\Microsoft.Azure.KeyVault.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Search, Version=10.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Search.10.1.0\lib\net461\Microsoft.Azure.Search.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Search.Common, Version=10.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Search.Common.10.1.0\lib\net461\Microsoft.Azure.Search.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Search.Data, Version=10.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Search.Data.10.1.0\lib\net461\Microsoft.Azure.Search.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Search.Service, Version=10.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Search.Service.10.1.0\lib\net461\Microsoft.Azure.Search.Service.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Services.AppAuthentication, Version=1.3.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Services.AppAuthentication.1.3.1\lib\net472\Microsoft.Azure.Services.AppAuthentication.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Storage.Common, Version=10.0.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Storage.Common.10.0.3\lib\net452\Microsoft.Azure.Storage.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Storage.Queue, Version=10.0.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Storage.Queue.10.0.3\lib\net452\Microsoft.Azure.Storage.Queue.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.1.1.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Data.Edm, Version=5.7.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Edm.5.7.0\lib\net40\Microsoft.Data.Edm.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.OData, Version=5.7.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.OData.5.7.0\lib\net40\Microsoft.Data.OData.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.Services.Client, Version=5.7.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Services.Client.5.7.0\lib\net40\Microsoft.Data.Services.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.DotNet.PlatformAbstractions, Version=2.1.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.DotNet.PlatformAbstractions.2.1.0\lib\net45\Microsoft.DotNet.PlatformAbstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.EntityFrameworkCore, Version=2.2.1.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.EntityFrameworkCore.2.2.1\lib\netstandard2.0\Microsoft.EntityFrameworkCore.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.EntityFrameworkCore.Abstractions, Version=2.2.1.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.EntityFrameworkCore.Abstractions.2.2.1\lib\netstandard2.0\Microsoft.EntityFrameworkCore.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.EntityFrameworkCore.Relational, Version=2.2.1.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.EntityFrameworkCore.Relational.2.2.1\lib\netstandard2.0\Microsoft.EntityFrameworkCore.Relational.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.EntityFrameworkCore.SqlServer, Version=2.2.1.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.EntityFrameworkCore.SqlServer.2.2.1\lib\netstandard2.0\Microsoft.EntityFrameworkCore.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite, Version=2.2.1.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite.2.2.1\lib\netstandard2.0\Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Caching.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Caching.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Memory, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Caching.Memory.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Caching.Memory.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Binder.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.2.2.0\lib\net461\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyModel, Version=2.1.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyModel.2.1.0\lib\net451\Microsoft.Extensions.DependencyModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.FileProviders.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Hosting.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Hosting.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Debug, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Debug.2.0.0\lib\netstandard2.0\Microsoft.Extensions.Logging.Debug.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.ObjectPool, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.ObjectPool.2.2.0\lib\netstandard2.0\Microsoft.Extensions.ObjectPool.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Clients.ActiveDirectory, Version=4.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Clients.ActiveDirectory.4.3.0\lib\net45\Microsoft.IdentityModel.Clients.ActiveDirectory.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=1.0.0.127, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.1.0.0\lib\net451\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=5.0.0.127, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.5.0.0\lib\net451\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Net.Http.Headers, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.Headers.2.2.0\lib\netstandard2.0\Microsoft.Net.Http.Headers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Rest.ClientRuntime, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Rest.ClientRuntime.2.3.20\lib\net461\Microsoft.Rest.ClientRuntime.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Rest.ClientRuntime.Azure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Rest.ClientRuntime.Azure.3.3.19\lib\net461\Microsoft.Rest.ClientRuntime.Azure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Spatial, Version=7.5.3.21218, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Spatial.7.5.3\lib\portable-net45+win8+wpa81\Microsoft.Spatial.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=1*******, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Types.14.0.1016.290\lib\net40\Microsoft.SqlServer.Types.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Configuration, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.WindowsAzure.ConfigurationManager.3.2.3\lib\net40\Microsoft.WindowsAzure.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=9.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAzure.Storage.9.0.0\lib\net45\Microsoft.WindowsAzure.Storage.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage.DataMovement, Version=0.7.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Storage.DataMovement.0.7.1\lib\net45\Microsoft.WindowsAzure.Storage.DataMovement.dll</HintPath>
    </Reference>
    <Reference Include="Mindscape.Raygun4Net, Version=5.7.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Mindscape.Raygun4Net.Core.5.7.0\lib\net40\Mindscape.Raygun4Net.dll</HintPath>
    </Reference>
    <Reference Include="Mindscape.Raygun4Net.WebApi, Version=5.7.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Mindscape.Raygun4Net.WebApi.5.7.0\lib\net45\Mindscape.Raygun4Net.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite, Version=1.15.3.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.Core.1.15.3\lib\net45\NetTopologySuite.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite.CoordinateSystems, Version=1.15.3.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.CoordinateSystems.1.15.3\lib\net45\NetTopologySuite.CoordinateSystems.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite.IO.SqlServerBytes, Version=*******, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.IO.SqlServerBytes.1.15.0\lib\netstandard2.0\NetTopologySuite.IO.SqlServerBytes.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Ninject, Version=3.3.4.0, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.3.3.4\lib\net45\Ninject.dll</HintPath>
    </Reference>
    <Reference Include="Ninject.Extensions.Conventions, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.Extensions.Conventions.3.3.0\lib\net45\Ninject.Extensions.Conventions.dll</HintPath>
    </Reference>
    <Reference Include="Ninject.Extensions.Factory, Version=3.3.2.0, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.Extensions.Factory.3.3.2\lib\net45\Ninject.Extensions.Factory.dll</HintPath>
    </Reference>
    <Reference Include="Ninject.Web.Common, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.Web.Common.3.3.0\lib\net45\Ninject.Web.Common.dll</HintPath>
    </Reference>
    <Reference Include="Ninject.Web.Common.WebHost, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.Web.Common.WebHost.3.3.0\lib\net45\Ninject.Web.Common.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="Ninject.Web.WebApi, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.Web.WebApi.3.3.0\lib\net45\Ninject.Web.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Ninject.Web.WebApi.WebHost, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.Web.WebApi.WebHost.3.3.0\lib\net45\Ninject.Web.WebApi.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.7.2\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="NLog.Web, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.Web.4.9.2\lib\net35\NLog.Web.dll</HintPath>
    </Reference>
    <Reference Include="Polly, Version=*******, Culture=neutral, PublicKeyToken=c8a3ffc3f8f825cc, processorArchitecture=MSIL">
      <HintPath>..\packages\Polly.7.1.0\lib\netstandard2.0\Polly.dll</HintPath>
    </Reference>
    <Reference Include="PreMailer.Net, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\PreMailer.Net.1.3.0\lib\net40\PreMailer.Net.dll</HintPath>
    </Reference>
    <Reference Include="RazorEngine, Version=*******, Culture=neutral, PublicKeyToken=9ee697374c7e744a, processorArchitecture=MSIL">
      <HintPath>..\packages\RazorEngine.3.9.0\lib\net45\RazorEngine.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq, Version=*******, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.2.2.0\lib\net45\Remotion.Linq.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=10*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.105.2.3\lib\net46\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.Events.Core, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.Events.Core.1.0.314\lib\netstandard2.0\RoofSnap.Events.Core.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.Functions.Models, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.Functions.Models.1.0.18\lib\netstandard2.0\RoofSnap.Functions.Models.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.GeoCode.Client, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.GeoCode.Client.1.0.15\lib\netstandard2.0\RoofSnap.GeoCode.Client.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.GeoCode.Model, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.GeoCode.Model.1.0.15\lib\netstandard2.0\RoofSnap.GeoCode.Model.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.GraphingUtilities, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.GraphingUtilities.1.0.7\lib\net45\RoofSnap.GraphingUtilities.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.Measurements, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.Measurements.1.1.12\lib\netstandard2.0\RoofSnap.Measurements.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.Search.Models, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.Search.Models.1.0.16\lib\netstandard2.0\RoofSnap.Search.Models.dll</HintPath>
    </Reference>
    <Reference Include="SalesforceMagic, Version=0.2.4.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SalesforceMagic.0.2.3.8\lib\net45\SalesforceMagic.dll</HintPath>
    </Reference>
    <Reference Include="SendGrid, Version=9.8.0.0, Culture=neutral, PublicKeyToken=4f047e93159395ca, processorArchitecture=MSIL">
      <HintPath>..\packages\Sendgrid.9.9.0\lib\net452\SendGrid.dll</HintPath>
    </Reference>
    <Reference Include="SendGrid.SmtpApi, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SendGrid.SmtpApi.1.3.3\lib\SendGrid.SmtpApi.dll</HintPath>
    </Reference>
    <Reference Include="StackifyHttpTracer, Version=*******, Culture=neutral, PublicKeyToken=93c44ce23f2048dd, processorArchitecture=MSIL">
      <HintPath>..\packages\StackifyHttpModule.1.0.41\lib\net40\StackifyHttpTracer.dll</HintPath>
    </Reference>
    <Reference Include="Stripe.net, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Stripe.net.47.3.0-beta.1\lib\net461\Stripe.net.dll</HintPath>
    </Reference>
    <Reference Include="Swashbuckle.Core, Version=*******, Culture=neutral, PublicKeyToken=cd1bb07a5ac7c7bc, processorArchitecture=MSIL">
      <HintPath>..\packages\Swashbuckle.Core.5.6.0\lib\net40\Swashbuckle.Core.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.0\lib\netstandard2.0\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Collections.Immutable.1.5.0\lib\netstandard2.0\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.SqlClient, Version=4.5.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Data.SqlClient.4.6.0\lib\net461\System.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.4.5.0\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=5.0.0.127, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.5.0.0\lib\net451\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.Interactive.Async, Version=3.2.0.0, Culture=neutral, PublicKeyToken=94bc3704cddfc263, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Interactive.Async.3.2.0\lib\net46\System.Interactive.Async.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL" />
    <Reference Include="System.Linq, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.4.1.0\lib\net463\System.Linq.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.Expressions.4.1.0\lib\net463\System.Linq.Expressions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.1\lib\netstandard2.0\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.Extensions, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Primitives, Version=4.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.4.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.4.1.0\lib\net462\System.Reflection.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.2\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Extensions.4.1.0\lib\net462\System.Runtime.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices" />
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Spatial, Version=5.7.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Spatial.5.7.0\lib\net40\System.Spatial.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.4.5.0\lib\netstandard2.0\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.2\lib\netstandard2.0\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread" />
    <Reference Include="System.Web.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.2.7\lib\net45\System.Web.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Cors.5.2.7\lib\net45\System.Web.Http.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.3\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebActivatorEx, Version=*******, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.1.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebApi.Hal, Version=2.6.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\WebApi.Hal.2.6.0\lib\net45\WebApi.Hal.dll</HintPath>
    </Reference>
    <Reference Include="WebMatrix.Data, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.Data.3.2.3\lib\net45\WebMatrix.Data.dll</HintPath>
    </Reference>
    <Reference Include="WebMatrix.WebData, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.WebData.3.2.3\lib\net45\WebMatrix.WebData.dll</HintPath>
    </Reference>
    <Reference Include="XkPassword, Version=1.1.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\XkPassword.1.1.2.0\lib\net35\XkPassword.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Areas\AI\AiTestController.cs" />
    <Compile Include="Areas\AI\IAiService.cs" />
    <Compile Include="Areas\AI\QuestionPrompt.cs" />
    <Compile Include="Areas\Billing\BillingTransaction.cs" />
    <Compile Include="Areas\Billing\BillingTransactionEventData.cs" />
    <Compile Include="Areas\Billing\BillingTransactionResult.cs" />
    <Compile Include="Areas\Billing\BillingTransactionResultFactory.cs" />
    <Compile Include="Areas\Billing\IBillingSettlementCallbacks.cs" />
    <Compile Include="Areas\Billing\Receipts\CancellationReceipt\CancellationReceiptTemplate.cs" />
    <Compile Include="Areas\Billing\Receipts\CancellationReceipt\CancellationReceiptMailer.cs" />
    <Compile Include="Areas\Billing\Receipts\DowngradeReceipt\DowngradeReceiptMailer.cs" />
    <Compile Include="Areas\Billing\Receipts\DowngradeReceipt\DowngradeReceiptTemplate.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\SubscriptionReceiptUpdatePaymentMethodInfo.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\TransactionBillingFailedInfo.cs" />
    <Compile Include="Areas\Billing\Receipts\SubscriptionBillingFailed\SubscriptionBillingFailedMailer.cs" />
    <Compile Include="Areas\Billing\Receipts\SubscriptionBillingFailed\SubscriptionBillingFailedTemplate.cs" />
    <Compile Include="Areas\Billing\Receipts\SubscriptionReceiptsProcessor.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\SubscriptionReceiptModelFactoryDecorator.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\SubscriptionReceiptModelFactory.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\TransactionReceiptBillingContactInfo.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\SubscriptionReceiptBillingFrequencyInfo.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\TransactionReceiptCreditCardInfo.cs" />
    <Compile Include="Areas\Billing\Receipts\Models\ReceiptModel.cs" />
    <Compile Include="Areas\Billing\Receipts\SubscriptionProfile.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionProfile.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\SubscriptionReceiptCancellationUrlInfo.cs" />
    <Compile Include="Areas\BraintreeWebhooks\SubscriptionIdNotFoundException.cs" />
    <Compile Include="Areas\BraintreeWebhooks\WrongNotificationKindException.cs" />
    <Compile Include="Areas\Documents\ExternalImplementer\ExternalImplementerDecorator.cs" />
    <Compile Include="Areas\Documents\InputReplacementChain\InputChainNotPopulatedException.cs" />
    <Compile Include="Areas\Documents\InputReplacementChain\RenderingSanitizer.cs" />
    <Compile Include="Areas\Documents\InputReplacementChain\SignatureReplacementHandler.cs" />
    <Compile Include="Areas\Documents\InputReplacementChain\CheckBoxReplacementHandler.cs" />
    <Compile Include="Areas\Documents\InputReplacementChain\WorkflowBarReplacementHandler.cs" />
    <Compile Include="Areas\Documents\Project\CMC\CMCDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Office\ProjectOfficeHtmlDecorator.cs" />
    <Compile Include="Areas\Documents\Project\SketchOrderNotificationDocumentData.cs" />
    <Compile Include="Areas\Documents\Project\SketchOrder\GuttersDecorator.cs" />
    <Compile Include="Areas\Documents\Project\SketchOrder\MetalRoofDecorator.cs" />
    <Compile Include="Areas\Documents\Representations\DocumentV2ExternalImplementerValueRepresentation.cs" />
    <Compile Include="Areas\Documents\SketchOrderNotificationDocumentDataFactory.cs" />
    <Compile Include="Areas\Documents\Templates\ExpectedGlobalFileNameException.cs" />
    <Compile Include="Areas\Documents\Templates\TemplateHtmlRequiredException.cs" />
    <Compile Include="Areas\ErrorTest\TestException.cs" />
    <Compile Include="Areas\Estimating\EstimatesMailer\EstimateEmail.cs" />
    <Compile Include="Areas\Estimating\EstimatesMailer\EstimateEmailLink.cs" />
    <Compile Include="Areas\Estimating\EstimatesMailer\EstimateEmailOption.cs" />
    <Compile Include="Areas\Estimating\EstimatesMailer\EstimateEmailTemplates.cs" />
    <Compile Include="Areas\Estimating\EstimatesMailer\EstimateEmailOptionItem.cs" />
    <Compile Include="Areas\Estimating\EstimatesMailer\EstimateResponseSender.cs" />
    <Compile Include="Areas\Estimating\EstimatesMailer\EstimatesMailer.cs" />
    <Compile Include="Areas\EverProEdge\EverProEdgeController.cs" />
    <Compile Include="Areas\EverProEdge\EverProEdgeService.cs" />
    <Compile Include="Areas\HomeController.cs" />
    <Compile Include="Areas\Homeowner\HomeownerController.cs" />
    <Compile Include="Areas\Nearmap\Billing\CreateNearmapOrderTransactionBatchDto.cs" />
    <Compile Include="Areas\Nearmap\Billing\NearmapOrderTransactionBatchAuthFilter.cs" />
    <Compile Include="Areas\Nearmap\Billing\ExcludedOrganizationIds.cs" />
    <Compile Include="Areas\Nearmap\Billing\Mail\Models\NearmapTransactionAdminSummaryItem.cs" />
    <Compile Include="Areas\Nearmap\Billing\Mail\Models\NearmapTransactionError.cs" />
    <Compile Include="Areas\Nearmap\Billing\Mail\Models\NearmapTransactionSummary.cs" />
    <Compile Include="Areas\Nearmap\Billing\Mail\Models\NearmapTransactionUserSummaryItem.cs" />
    <Compile Include="Areas\Nearmap\Billing\Mail\NearmapOrderHandlebarsTemplates.cs" />
    <Compile Include="Areas\Nearmap\Billing\Mail\NearmapOrderHtmlTemplates.cs" />
    <Compile Include="Areas\Nearmap\Billing\Mail\NearmapOrderMailer.cs" />
    <Compile Include="Areas\Nearmap\Billing\NearmapBiller.cs" />
    <Compile Include="Areas\Nearmap\Billing\NearmapOrderTransactionsBatchController.cs" />
    <Compile Include="Areas\Nearmap\NearmapImagery\RoofSnapImageryCoverageResponse.cs" />
    <Compile Include="Areas\Nearmap\NearmapOrders\InsufficientOrganizationCreditBalanceException.cs" />
    <Compile Include="Areas\Offices\ContractTerms\OfficeContractTermsService.cs" />
    <Compile Include="Areas\Offices\ContractTerms\IOfficeContractTermsService.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\RoundToOrderableQuantityChecker.cs" />
    <Compile Include="Areas\Offices\OfficeCloner.cs" />
    <Compile Include="Areas\Offices\OfficeContractTerms.cs" />
    <Compile Include="Areas\Offices\OfficeContractTermsDto.cs" />
    <Compile Include="Areas\Offices\OfficeContractTermsRepresentation.cs" />
    <Compile Include="Areas\Offices\OfficeNotFoundException.cs" />
    <Compile Include="Areas\Offices\UpdateOfficeDto.cs" />
    <Compile Include="Areas\Organizations\OrganizationEstimateTemplatesFilter.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\EnableEasierEstimatesOrganizationFeatureStrategy.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\EnableGuttersOrganizationFeatureStrategy.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\EnableMetalRoofOrganizationFeatureStrategy.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\EnableSmartEstimatesOrganizationFeatureStrategy.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\RushOrderingFeatureStrategy.cs" />
    <Compile Include="Areas\Organizations\OrganizationRushOrderSettingsRepresentation.cs" />
    <Compile Include="Areas\Organizations\OrganizationSketchOrderPriceGridDto.cs" />
    <Compile Include="Areas\Organizations\OrganizationSketchOSPriceGridRepresentation.cs" />
    <Compile Include="Areas\Organizations\OrganizationSketchOrderPriceGridFactory.cs" />
    <Compile Include="Areas\Organizations\OrganizationUploads\OrganizationUploadsService.cs" />
    <Compile Include="Areas\Organizations\SketchOrderPriceProfile.cs" />
    <Compile Include="Areas\Passwords\InvalidPasswordException.cs" />
    <Compile Include="Areas\Payments\RemoteLinkRequest.cs" />
    <Compile Include="Areas\Payments\PaymentRequest.cs" />
    <Compile Include="Areas\Payments\PaymentIntentRequest.cs" />
    <Compile Include="Areas\Payments\PaymentsController.cs" />
    <Compile Include="Areas\Payments\StripePayment\CheckoutSession.cs" />
    <Compile Include="Areas\Payments\StripePayment\IStripeService.cs" />
    <Compile Include="Areas\Payments\StripePayment\OfficeTemplateData.cs" />
    <Compile Include="Areas\Payments\StripePayment\OnBoardingError.cs" />
    <Compile Include="Areas\Payments\StripePayment\PaymentIntent.cs" />
    <Compile Include="Areas\Payments\StripePayment\PaymentIntentDto.cs" />
    <Compile Include="Areas\Payments\StripePayment\PaymentLinkMailer.cs" />
    <Compile Include="Areas\Payments\StripePayment\PaymentLinkEmailTemplate.cs" />
    <Compile Include="Areas\Payments\StripePayment\StripeAccount.cs" />
    <Compile Include="Areas\Payments\StripePayment\StripePaymentRequestDto.cs" />
    <Compile Include="Areas\Payments\StripePayment\StripeService.cs" />
    <Compile Include="Areas\Payments\StripePayment\StripeProfile.cs" />
    <Compile Include="Areas\Payments\StripePayment\Events\IStripeEvent.cs" />
    <Compile Include="Areas\Payments\StripePayment\Events\StripeAuthFilter.cs" />
    <Compile Include="Areas\Payments\StripePayment\Events\StripeEvent.cs" />
    <Compile Include="Areas\Payments\StripePayment\Events\StripeExtensions.cs" />
    <Compile Include="Areas\Payments\StripePayment\Events\IStripeEventParser.cs" />
    <Compile Include="Areas\Payments\StripePayment\Events\StripeEventParser.cs" />
    <Compile Include="Areas\Payments\StripePayment\Events\StripeEventController.cs" />
    <Compile Include="Areas\Projects\EstimateTemplateCloner.cs" />
    <Compile Include="Areas\Projects\EstimateTemplateItemProfile.cs" />
    <Compile Include="Areas\Projects\EstimateTemplateProfile.cs" />
    <Compile Include="Areas\AI\AiService.cs" />
    <Compile Include="Areas\Projects\Estimating\AllEstimateRepresentation.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\MetalRoofReport.cs" />
    <Compile Include="Areas\Projects\FaceTypeConstants.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\GutterReport.cs" />
    <Compile Include="Areas\Projects\OfficePricedChargeableItemNotFoundException.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\EdgeSubTypeConversionException.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Vertexes\MultiMaterialVertexDetectedException.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\ProjectDrawingEstimateItemSynchronizer.cs" />
    <Compile Include="Areas\Projects\ProjectImageGeneratorQueueService.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageV2Dto.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageGeneratedServiceModel.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageWireframeLinkTemplates.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageWireframeRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectImages\WireframeImageOptionsDto.cs" />
    <Compile Include="Areas\Projects\ProjectRestoreFailedException.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\DrawingRelationships\ThreeDimensionalConversionException.cs" />
    <Compile Include="Areas\Projects\UnableToGiveAProjectToDifferentOfficeException.cs" />
    <Compile Include="Areas\Salesforce\SalesforceLead.cs" />
    <Compile Include="Areas\Salesforce\SalesforceOpportunityLineItem.cs" />
    <Compile Include="Areas\Salesforce\SalesforceCase.cs" />
    <Compile Include="Areas\Salesforce\SalesforceOpportunity.cs" />
    <Compile Include="Areas\Salesforce\SalesforceAccount.cs" />
    <Compile Include="Areas\Salesforce\SalesforceContact.cs" />
    <Compile Include="Areas\Salesforce\SalesforcePricebook.cs" />
    <Compile Include="Areas\Salesforce\SalesforcePricebookEntry.cs" />
    <Compile Include="Areas\Salesforce\SalesforceProduct.cs" />
    <Compile Include="Areas\Salesforce\SalesforceRepository.cs" />
    <Compile Include="Areas\Salesforce\SalesforceSynchronizationService.cs" />
    <Compile Include="Areas\Salesforce\SalesTeamSyncErrorMailer.cs" />
    <Compile Include="Areas\SignUp\OrganizationCreationFailedException.cs" />
    <Compile Include="Areas\SignUp\ProfileAlreadyExistsException.cs" />
    <Compile Include="Areas\SignUp\SignUp.cs" />
    <Compile Include="Areas\SignUp\SignupDtoV4.cs" />
    <Compile Include="Areas\SignUp\SignupDtoV3.cs" />
    <Compile Include="Areas\SignUp\SignupDtoV2.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbRushOrderSettings.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbSketchOrderRevisionNote.cs" />
    <Compile Include="Areas\SketchOrders\Data\RushOrderSettingsConfiguration.cs" />
    <Compile Include="Areas\SketchOrders\Data\SketchOrderRevisionNoteConfiguration.cs" />
    <Compile Include="Areas\SketchOrders\Dtos\V2\CreateSketchOrderRevisionNoteDto.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderRevisionNoteRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\OrganizationHasNoEstimateTemplatesException.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbSketchOrderGroup.cs" />
    <Compile Include="Areas\SketchOrders\Data\SketchOrderGroupConfiguration.cs" />
    <Compile Include="Areas\SketchOrders\Dtos\V2\CreateExternalImplementerSketchReportDto.cs" />
    <Compile Include="Areas\SketchOrders\Dtos\V2\UpdateExternalSketchOrderV2Dto.cs" />
    <Compile Include="Areas\SketchOrders\ExternalDocumentFactory.cs" />
    <Compile Include="Areas\SketchOrders\ExternalImplementerService.cs" />
    <Compile Include="Areas\SketchOrders\HAL\NotificationCustomDisplayFactory.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\EmbeddedSketchOrderGroupRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\ExternalImplementerSketchOrderListV2Representation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\ExternalImplementerSketchOrderV2LinkTemplates.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\ExternalSharedAccessSignatureTokenRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\ExternalSketchOrderV2Representation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\MapMarkerRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\SketchTechSketchOrderV2Representation.cs" />
    <Compile Include="Areas\SketchOrders\InvalidSketchOrderStatusTransitionExceptionAttribute.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTotalsSummary.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTransactionBatchV2AuthFilter.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSMailer\SketchOSEmailTemplates.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\InsufficientFreeSketchOrderBalanceException.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\PendingGroupBillingSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\RevisionRequestedSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\Mailer\Refund\SketchOrderRefundInfoFactory.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\SketchOrderBillingBatchException.cs" />
    <Compile Include="Areas\SketchOrders\V2\ExternalImplementerSketchOrdersV2Controller.cs" />
    <Compile Include="Areas\SnapEstimates\BaseSnapEstimateDto.cs" />
    <Compile Include="Areas\SnapEstimates\ISnapEstimateService.cs" />
    <Compile Include="Areas\SnapEstimates\LineItemDto.cs" />
    <Compile Include="Areas\SnapEstimates\SnapEstimateRepresentation.cs" />
    <Compile Include="Areas\SnapEstimates\SnapEstimatesController.cs" />
    <Compile Include="Areas\SnapEstimates\CreateSnapEstimateRequestDto.cs" />
    <Compile Include="Areas\SnapEstimates\SnapEstimateService.cs" />
    <Compile Include="Areas\SnapEstimates\SnapEstimateProfile.cs" />
    <Compile Include="Areas\SnapEstimates\SnapEstimateStatus.cs" />
    <Compile Include="Areas\SnapEstimates\UpdateSnapEstimateDto.cs" />
    <Compile Include="Areas\Subscriptions\SubscriptionController.cs" />
    <Compile Include="Areas\UserProfiles\BraintreeAddOnDiscrepancyException.cs" />
    <Compile Include="Areas\UserProfiles\BraintreeSubscriptionNotFoundException.cs" />
    <Compile Include="Areas\UserProfiles\CannotAddRoofSnapAdminRolesException.cs" />
    <Compile Include="Areas\UserProfiles\CreateUserProfileV2Dto.cs" />
    <Compile Include="Areas\UserProfiles\EdgeUserStatusUpdateDto.cs" />
    <Compile Include="Areas\UserProfiles\SignatureAuthorization.cs" />
    <Compile Include="Areas\WebHookSubscriptions\WebHookSubscriptionEventTypeNotSupportedException.cs" />
    <Compile Include="Common\BlobStorage\BlueRoofStorage.cs" />
    <Compile Include="Common\Braintree\AddOns\BraintreeUserAddOnException.cs" />
    <Compile Include="Common\Braintree\AddOns\BraintreeUserAddOnsNonMonthlySubscriptionPlanException.cs" />
    <Compile Include="Common\Braintree\AddOns\BraintreeUnableToModifyCanceledSubscriptionException.cs" />
    <Compile Include="Common\Braintree\AddOns\RemoveBraintreeUserAddOnException.cs" />
    <Compile Include="Common\Config\FeatureFlags.cs" />
    <Compile Include="Common\Data\Events\DbSketchOrderCrudEventHandler.cs" />
    <Compile Include="Common\Data\Events\OrganizationCrudEventHandler.cs" />
    <Compile Include="Common\Handlebars\CurrentDateHelper.cs" />
    <Compile Include="Common\Handlebars\ESTCurrentDateHelper.cs" />
    <Compile Include="Common\Handlebars\IsEvenIndexHelper.cs" />
    <Compile Include="Common\Handlebars\IsOddIndexHelper.cs" />
    <Compile Include="Common\Http\EverProEdgeClient.cs" />
    <Compile Include="Common\Http\HttpStatusCodeExtensions.cs" />
    <Compile Include="Common\HubSpot\HubSpotApiResponseException.cs" />
    <Compile Include="Common\Mail\EmailCompanyContactInfo.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\ReceiptIoCModule.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\TransactionReceiptModelFactory.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\TransactionReceiptModelFactoryDecorator.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\TransactionReceiptMailToAddresses.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\SubscriptionReceiptSubscriptionInfo.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\SubscriptionReceiptOrganizationInfo.cs" />
    <Compile Include="Areas\Billing\Receipts\ModelBuilding\TransactionReceiptTransactionInfo.cs" />
    <Compile Include="Areas\Billing\TransactionEvent.cs" />
    <Compile Include="Areas\Documents\API\HtmlFormDecorator.cs" />
    <Compile Include="Areas\Documents\CreateDocumentRequestFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportFormat.cs" />
    <Compile Include="Areas\Documents\DocumentRenderingFileName.cs" />
    <Compile Include="Areas\Documents\DocumentStorage.cs" />
    <Compile Include="Areas\Documents\DocumentsV2Service.cs" />
    <Compile Include="Areas\Documents\DocumentDecorator.cs" />
    <Compile Include="Areas\Documents\Estimate\OfficeCategorySettingsRepository.cs" />
    <Compile Include="Areas\Documents\Exceptions\EstimateOptionsRequiredException.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportItem.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportItemExtensionMethods.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportLaborItem.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportMaterialItem.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportColorItemFactory.cs" />
    <Compile Include="Areas\Documents\Estimate\LaborDecorator.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportLaborChargeCategory.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportMaterialChargeCategory.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportSummaryChargeCategory.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ISummary.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\LaborReport.cs" />
    <Compile Include="Areas\Documents\Estimate\MaterialDecorator.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\MaterialOrder.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ProjectRequiresOfficeException.cs" />
    <Compile Include="Areas\Documents\Exceptions\UserCanNotOpenDocumentException.cs" />
    <Compile Include="Areas\Documents\Exceptions\UserCanNotCreateDocumentException.cs" />
    <Compile Include="Areas\Documents\BlobDoesNotExistException.cs" />
    <Compile Include="Areas\Documents\InputReplacementChain\HtmlDocumentHandler.cs" />
    <Compile Include="Areas\Documents\InputReplacementChain\InputHandler.cs" />
    <Compile Include="Areas\Documents\InputReplacementChain\InputTagReplacementChain.cs" />
    <Compile Include="Areas\Documents\InputReplacementChain\ScriptReplacementHandler.cs" />
    <Compile Include="Areas\Documents\InputReplacementChain\SubmitReplacementHandler.cs" />
    <Compile Include="Areas\Documents\InputReplacementChain\TextBoxReplacementHandler.cs" />
    <Compile Include="Areas\Documents\OrganizationIdRequiredException.cs" />
    <Compile Include="Areas\Documents\Estimate\SummaryDecorator.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\SummaryReport.cs" />
    <Compile Include="Areas\Documents\Project\OrganizationSettingsDecorator.cs" />
    <Compile Include="Areas\Documents\UpdateDocumentV2FormDataDto.cs" />
    <Compile Include="Areas\Documents\API\ApiClientDecorator.cs" />
    <Compile Include="Areas\Documents\V1ToV2DocumentMapper.cs" />
    <Compile Include="Areas\Documents\Project\Billing\ProjectBillingDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Customer\ProjectCustomerDecorator.cs" />
    <Compile Include="Areas\DocumentTemplates\DocumentTemplateV2PermissionsController.cs" />
    <Compile Include="Areas\DocumentTemplates\DocumentTemplateV2PermissionRepresentation.cs" />
    <Compile Include="Areas\DocumentTemplates\DocumentV2TemplateCategoriesController.cs" />
    <Compile Include="Areas\DocumentTemplates\DocumentTemplatesV2Service.cs" />
    <Compile Include="Areas\DocumentTemplates\DocumentTemplateV2Profile.cs" />
    <Compile Include="Areas\DocumentTemplates\DocumentTemplateV2LinkTemplates.cs" />
    <Compile Include="Areas\DocumentTemplates\DocumentV2TemplateCategoryRepresentation.cs" />
    <Compile Include="Areas\DocumentTemplates\GlobalDocumentTemplates.cs" />
    <Compile Include="Areas\Documents\Exceptions\ProjectDrawingRequiredException.cs" />
    <Compile Include="Areas\Offices\Estimating\EstimateRules\EstimateRuleLinkTemplates.cs" />
    <Compile Include="Areas\Offices\Estimating\EstimateRules\EstimateRuleProfile.cs" />
    <Compile Include="Areas\Offices\Estimating\EstimateRules\EstimateRuleRepresentation.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\DbOfficeChargeCategory.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\SubItemRepresentation.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\OfficeChargeCategoryConfiguration.cs" />
    <Compile Include="Areas\Documents\CreateDocumentV2Dto.cs" />
    <Compile Include="Areas\Documents\Representations\DocumentRenderingV2Representation.cs" />
    <Compile Include="Areas\Documents\DocumentsV2AuthFilter.cs" />
    <Compile Include="Areas\Documents\DocumentV2LinkTemplates.cs" />
    <Compile Include="Areas\Documents\DocumentV2Profile.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\EstimateSubItemResolver.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemHideOnLaborReportInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\SteepChargeSystemIdToOfficePricedChargeableItemDictionaryFactory.cs" />
    <Compile Include="Areas\SignUp\Registration\CompleteRegistrationMailer.cs" />
    <Compile Include="Areas\SignUp\Registration\CompleteRegistrationTemplate.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\EmbeddedNotificationListRepresentation.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionReceiptMailer.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionReceiptTemplate.cs" />
    <Compile Include="Areas\UserProfiles\PaymentMethods\PaymentMethodError.cs" />
    <Compile Include="Areas\UserProfiles\PaymentMethods\PaymentMethodErrorCode.cs" />
    <Compile Include="Areas\UserProfiles\PaymentMethods\PaymentMethodVerificationErrorsFactory.cs" />
    <Compile Include="Areas\UserProfiles\PaymentMethods\PaymentMethodResult.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\EstimateReport.cs" />
    <Compile Include="Areas\Documents\Estimate\MultiEstimateDecorator.cs" />
    <Compile Include="Common\EnglishOrdinalSuffixConverter.cs" />
    <Compile Include="Common\Mail\EmailIoCModule.cs" />
    <Compile Include="Common\Mail\EmailModel.cs" />
    <Compile Include="Common\Mail\EmailModelFactoryDecorator.cs" />
    <Compile Include="Common\Mail\EmailModelFactory.cs" />
    <Compile Include="Common\Mail\NoEmailAddressException.cs" />
    <Compile Include="Common\Mail\SendGridEmailFailedException.cs" />
    <Compile Include="Common\Notifications\AcknowledgementMustImpactAssociatedNotificationException.cs" />
    <Compile Include="Common\Notifications\Data\DbNotificationCustomDisplayTemplate.cs" />
    <Compile Include="Common\Notifications\Data\NotificationCustomDisplayTemplateConfiguration.cs" />
    <Compile Include="Common\Notifications\NotificationDoesNotBelongToOwnerException.cs" />
    <Compile Include="Common\StringFormatter.cs" />
    <Compile Include="Common\Wrappers\QueueService.cs" />
    <Compile Include="Common\Braintree\BraintreeErrorCodes.cs" />
    <Compile Include="Common\BlobStorage\SharedAccessToken.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportEstimateChargeCategory.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportColorItem.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportEstimate.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\ReportEstimateItem.cs" />
    <Compile Include="Areas\Documents\Estimate\EstimateDecorator.cs" />
    <Compile Include="Areas\Documents\Templates\DocumentTemplateFactory.cs" />
    <Compile Include="Areas\Documents\Templates\DocumentTemplateReader.cs" />
    <Compile Include="Areas\Organizations\FreeSketchOrdersIncludedFeatureNotEnabledException.cs" />
    <Compile Include="Areas\Organizations\OrganizationFreeSketchOrderBalanceDto.cs" />
    <Compile Include="Areas\Organizations\OrganizationFreeSketchOrderBalanceProfile.cs" />
    <Compile Include="Areas\Organizations\OrganizationFreeSketchOrderRateDto.cs" />
    <Compile Include="Areas\Organizations\OrganizationFreeSketchOrderRateLessThanZeroException.cs" />
    <Compile Include="Areas\Organizations\OrganizationFreeSketchOrderBalanceRepresentation.cs" />
    <Compile Include="Areas\Organizations\OrganizationFreeSketchOrderBalanceLessThanZeroException.cs" />
    <Compile Include="Areas\Documents\Templates\DocumentTemplate.cs" />
    <Compile Include="Areas\Documents\Templates\ITemplateReader.cs" />
    <Compile Include="Areas\Organizations\V2\OrganizationFeatures\OrganizationFeaturesV2Representation.cs" />
    <Compile Include="Areas\Organizations\V2\OrganizationFeatures\OrganizationFeatureV2LinkTemplates.cs" />
    <Compile Include="Areas\Organizations\V2\OrganizationFeatures\OrganizationFeatureV2Profile.cs" />
    <Compile Include="Areas\Organizations\V2\OrganizationFeatures\OrganizationFeatureV2Representation.cs" />
    <Compile Include="Areas\Organizations\V2\OrganizationsV2Controller.cs" />
    <Compile Include="Areas\Organizations\OrganizationFreeSketchOrderRateProfile.cs" />
    <Compile Include="Areas\Organizations\OrganizationFreeSketchOrderRateRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\SketchOptionRequiresNotesException.cs" />
    <Compile Include="Areas\SketchOrders\Exceptions\InvalidSketchOrderOrganizationException.cs" />
    <Compile Include="Areas\SketchOrders\Exceptions\InvalidSketchOrderPaymentTypeException.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\AddOnsOrganizationFeatureStrategy.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\AllowSnappingGoogleImageryOrganizationFeatureStrategy.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\EstimatingOrganizationFeatureStrategy.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\FreeSketchOrdersIncludedOrganizationFeatureStrategy.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\OrganizationFeatureDto.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\OrganizationFeatureFactory.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\OrganizationFeatureStrategyFactory.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\OrganizationFeatureType.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\OrganizationFeatureStrategy.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\SketchServiceOrganizationFeatureStrategy.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderRefund.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\IRefundSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\SketchOrderStateAttributes.cs" />
    <Compile Include="Areas\Documents\DocumentGeneratorQueueService.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\FreeSketchOrderBalanceTransactionator.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\Mailer\Refund\SketchOrderRefundInfo.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\Mailer\Refund\SketchOrderRefundMailer.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\InReviewSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\CreateSketchOrderTransactionBatchDto.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTransactionBatchAuthFilter.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTransactionsBatchController.cs" />
    <Compile Include="Areas\SketchOrders\UnableToDeleteClonedProjectException.cs" />
    <Compile Include="Areas\Projects\ClonedProjectDto.cs" />
    <Compile Include="Areas\Projects\ProjectCannotBeClonedToADifferentOfficeException.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\SketchOrderBillingBatchTransactionFactory.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\SketchOrderBillingTransaction.cs" />
    <Compile Include="Areas\Engagement\EngagementFactory.cs" />
    <Compile Include="Areas\Engagement\EngagementRetriever.cs" />
    <Compile Include="Areas\Engagement\EngagementService.cs" />
    <Compile Include="Areas\Engagement\IEngagementService.cs" />
    <Compile Include="Areas\Engagement\OrganizationEngagement.cs" />
    <Compile Include="Areas\Engagement\UserProfileEngagement.cs" />
    <Compile Include="Areas\Jobs\JobOrganizationCloneDto.cs" />
    <Compile Include="Areas\Nearmap\NearmapImagery\PremiumImageryAvailabilityService.cs" />
    <Compile Include="Areas\Nearmap\NearmapImagery\NearmapSurveyModel.cs" />
    <Compile Include="Areas\Offices\OfficeDefaultEdgeSubTypes\OfficeDefaultEdgeSubTypeListRepresentation.cs" />
    <Compile Include="App_Start\RoofSnapEventsConfig.cs" />
    <Compile Include="Areas\Organizations\AdminOrganizationEngagementRepresentation.cs" />
    <Compile Include="Areas\Organizations\AdminOrganizationProjectEngagementListRepresentation.cs" />
    <Compile Include="Areas\Organizations\AdminOrganizationProjectEngagementRepresentation.cs" />
    <Compile Include="Areas\Organizations\DbOrderBilling.cs" />
    <Compile Include="Areas\Organizations\IOrganizationActivityDeterminer.cs" />
    <Compile Include="Areas\Organizations\NearmapProjectEngagementListRepresentation.cs" />
    <Compile Include="Areas\Organizations\NearmapProjectEngagementRepresentation.cs" />
    <Compile Include="Areas\Organizations\Organization.cs" />
    <Compile Include="Areas\Organizations\OrderBillingConfiguration.cs" />
    <Compile Include="Areas\Organizations\OrganizationActivityDeterminer.cs" />
    <Compile Include="Areas\Organizations\OrganizationDataSecurityChecker.cs" />
    <Compile Include="Areas\Organizations\OrganizationOverrideNotificationPriorityLevelConfiguration.cs" />
    <Compile Include="Areas\Organizations\PrimaryUserAuthFilter.cs" />
    <Compile Include="Areas\Organizations\ProjectMeasurementsValueAtPitchEngagementsListRepresentation.cs" />
    <Compile Include="Areas\Organizations\ProjectMeasurementsValueAtPitchEngagementsRepresentation.cs" />
    <Compile Include="Areas\Organizations\SubscriptionLinkTemplates.cs" />
    <Compile Include="Areas\Organizations\RoofSnapSubscription.cs" />
    <Compile Include="Areas\Organizations\RoofSnapSubscriptionFactory.cs" />
    <Compile Include="Areas\Organizations\SubscriptionProfile.cs" />
    <Compile Include="Areas\Organizations\SubscriptionRepresentation.cs" />
    <Compile Include="Areas\Organizations\SubscriptionTypeNotFoundException.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\ProjectDrawing\DbProjectDrawingExtensions.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\ICreateEstimateItemStrategy.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\IUnitQuantityCalculator.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\OfficeCustomCategoryConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\ColorIdToColorOptionRepresentationResolver.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\EachTypeMeasurementFromCategoryCalculator.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\EstimateItemColorOptionResolver.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\EstimateItemColorOptionsCountResolver.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\EstimateItemLinkTemplates.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\EstimateItemListRepresentation.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\EstimateItemMaterialItemAlreadyExistsException.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\EstimateItemProfile.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\EstimateItemRepresentation.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\EstimateItemsFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetTypeMeasurementFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\SquareTypeMeasurementFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\ApronFlashingUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\DownSpoutsUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\EaveEdgeUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\GuttersUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\GutterToppersUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\HipAndRidgeCapUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\IceWaterShieldUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\ILinearFeetTypeUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\RakeEdgeUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\RidgeVentUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\StarterShinglesUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\StepFlashingUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\LinearFeetCalculators\ValleysUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\EdgeSubTypeConverter.cs" />
    <Compile Include="Areas\Offices\OfficeDefaultEdgeSubTypes\OfficeDefaultEdgeSubTypeRepresentation.cs" />
    <Compile Include="Areas\Offices\OfficeDefaultEdgeSubTypes\OfficeDefaultEdgeSubTypesLinkTemplates.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Faces\FaceTypeConverter.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\ProjectDrawingSummary.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\ProjectDrawingSummaryLinkTemplates.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\ProjectDrawingSummaryProfile.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\ProjectDrawingSummaryRepresentation.cs" />
    <Compile Include="Areas\Projects\Search\AzureProjectSearchFilterFactory.cs" />
    <Compile Include="Areas\Projects\Search\AzureProjectSearchParametersFactory.cs" />
    <Compile Include="Areas\Projects\Search\ProjectDocumentSearchWrapper.cs" />
    <Compile Include="Areas\Projects\Search\ProjectSearch.cs" />
    <Compile Include="Areas\Projects\Search\IProjectSearch.cs" />
    <Compile Include="Areas\Projects\Search\ProjectSearchRequest.cs" />
    <Compile Include="Areas\Projects\Search\SqlProjectSearch.cs" />
    <Compile Include="Areas\Projects\UnableToGiveAProjectToDifferentOrganizationException.cs" />
    <Compile Include="Areas\Projects\UpdateProjectOwnerDto.cs" />
    <Compile Include="Areas\Projects\ProjectShareDto.cs" />
    <Compile Include="Areas\Projects\ProjectShareException.cs" />
    <Compile Include="Areas\Projects\ProjectShareLinkTemplates.cs" />
    <Compile Include="Areas\Projects\ProjectShareListRepresentation.cs" />
    <Compile Include="Areas\Projects\SharedProjectProfile.cs" />
    <Compile Include="Areas\Projects\ProjectShareRepresentation.cs" />
    <Compile Include="Areas\Projects\Search\AzureProjectSearch.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\SketchOrderRefundTransaction.cs" />
    <Compile Include="Areas\SketchOrders\V2SketchOrderOwnerRouteSwitch.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbOrganizationOverrideNotificationPriorityLevel.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbSketchOrderBillingTransaction.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbSketchOrderReportingOption.cs" />
    <Compile Include="Areas\SketchOrders\Data\SketchOptions.cs" />
    <Compile Include="Areas\SketchOrders\Data\SketchOrderBillingTransactionConfiguration.cs" />
    <Compile Include="Areas\DocumentTemplates\DocumentTemplatesV2Controller.cs" />
    <Compile Include="Areas\DocumentTemplates\DocumentTemplateV2Representation.cs" />
    <Compile Include="Areas\Documents\Representations\SharedAccessSignatureTokenRepresentation.cs" />
    <Compile Include="Areas\Documents\Representations\DocumentV2Representation.cs" />
    <Compile Include="Areas\Documents\DocumentsV2Controller.cs" />
    <Compile Include="Common\Auth\NoAuthenticatedUserException.cs" />
    <Compile Include="Common\BlobStorage\OrganizationStorage.cs" />
    <Compile Include="Areas\Documents\DocumentData.cs" />
    <Compile Include="Areas\Documents\DocumentDataIoCModule.cs" />
    <Compile Include="Areas\Documents\Project\Drawing\ProjectDrawingDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Images\ProjectImagesDrawingDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Images\ProjectImagesAspectDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Images\ProjectImagesDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Measurements\ProjectMeasurementsAreaPitchDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Office\ProjectOfficeAddressDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Office\ProjectOfficeReportOptionDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Owner\ProjectOwnerContactInfoNameDecorator.cs" />
    <Compile Include="Areas\Documents\Project\ProjectNotesDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Office\ProjectOfficeDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Owner\ProjectOwnerContactInfoDecorator.cs" />
    <Compile Include="Areas\Documents\Project\ProjectDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Measurements\ProjectMeasurementsDecorator.cs" />
    <Compile Include="Areas\Documents\DocumentDataContext.cs" />
    <Compile Include="Areas\Documents\DocumentDataDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Measurements\ProjectMeasurementsAreaDecorator.cs" />
    <Compile Include="Areas\Documents\Project\Measurements\ProjectMeasurementsLinearSubEdgeTypesDecorator.cs" />
    <Compile Include="Areas\Documents\IDocumentData.cs" />
    <Compile Include="Areas\Documents\Project\Measurements\ProjectMeasurementsLinearEdgeTypesDecorator.cs" />
    <Compile Include="Areas\Documents\Project\SketchOrder\ProjectSketchOrderDecorator.cs" />
    <Compile Include="Common\Data\EagerLoadingExtensions.cs" />
    <Compile Include="Common\EndpointNotImplementedException.cs" />
    <Compile Include="Common\EntityNotFoundException.cs" />
    <Compile Include="Common\EnumExtensions.cs" />
    <Compile Include="Common\Notifications\Data\DbNotificationAcknowledgement.cs" />
    <Compile Include="Common\Notifications\Data\NotificationAcknowledgementRepresentation.cs" />
    <Compile Include="Common\Notifications\Data\NotificationAcknowledgementConfiguration.cs" />
    <Compile Include="Areas\SketchOrders\Data\SketchOrderDeliveryOptions.cs" />
    <Compile Include="Areas\SketchOrders\Data\SketchOrderReportingOptionConfiguration.cs" />
    <Compile Include="Areas\SketchOrders\HAL\EmbeddedSketchOrderRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\SketchOrderNotificationService.cs" />
    <Compile Include="Common\Notifications\DTOs\NotificationAcknowledgementDto.cs" />
    <Compile Include="Common\Notifications\Hal\NotificationLinkTemplates.cs" />
    <Compile Include="Common\Notifications\NotificationService.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\SketchOrderBillingTransactionRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\OrganizationSketchOrderEngagement.cs" />
    <Compile Include="Areas\SketchOrders\OrganizationSketchOrderEngagementRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\SketchAdminSketchOrderAuthFilter.cs" />
    <Compile Include="Areas\SketchOrders\CustomerSketchOrderAuthFilter.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbOrganizationOverrideSketchCostTiers.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbSketchOrderCosts.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbSketchOrderTier.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbSketchOrderTiersByFacet.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbSketchOrderTiersBySquare.cs" />
    <Compile Include="Areas\SketchOrders\Data\SketchOrderTierConfiguration.cs" />
    <Compile Include="Areas\SketchOrders\DrawingRequestConversionHandler.cs" />
    <Compile Include="Areas\SketchOrders\Dtos\V2\IUpdateSketchOrderV2Dto.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\SketchAdminSketchOrderListV2Representation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\SketchAdminSketchOrderV2LinkTemplates.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\SketchAdminSketchOrderV2Representation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\SketchOrderV2Representation.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderAuthFilter.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderEngagementRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderQueryableExtensions.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderEngagement.cs" />
    <Compile Include="Areas\Engagement\SketchOrderEngagementsFactory.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderPaymentService.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderReportingOptionService.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTiers\SketchOrderTiersService.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\DrawingRequest.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\DrawingRequestFactory.cs" />
    <Compile Include="Areas\Offices\OfficeDefaultEdgeSubTypes\OfficeDefaultEdgeSubTypeDto.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\BillingFailedSketchOrderEmailer.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\ISketchOrderStateFactory.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\OfficeDefaultEdgeSubTypeHandler.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\OfficeDefaultEdgeSubTypesApplicationMeasurementsHandler.cs" />
    <Compile Include="Areas\SketchOrders\Dtos\V2\CreateSketchOrderV2Dto.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderDeliverer.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\BillingFailedSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\SketchOrderBillingBatchTransaction.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\SketchOrderBillingConstants.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\SketchOrderBillingTransactionAuditRepository.cs" />
    <Compile Include="Areas\SketchOrders\UserProfileSketchOrderEngagement.cs" />
    <Compile Include="Areas\SketchOrders\UserProfileSketchOrderEngagementRepresentation.cs" />
    <Compile Include="Areas\Organizations\OrganizationFromSubscriptionRetriever.cs" />
    <Compile Include="Areas\SketchOrders\V2\SketchOrderBillingTransactionListRepresentation.cs" />
    <Compile Include="Areas\Subscriptions\SubscriptionManager.cs" />
    <Compile Include="Areas\Subscriptions\BraintreeSubscriptionIdFactory.cs" />
    <Compile Include="Areas\Subscriptions\SubscriptionSearcher.cs" />
    <Compile Include="Areas\UserProfiles\AdminUserProfileProjectEngagementListRepresentation.cs" />
    <Compile Include="Areas\UserProfiles\AdminUserProfileProjectEngagementRepresentation.cs" />
    <Compile Include="Areas\UserProfiles\AdminUserProfileListRepresentation.cs" />
    <Compile Include="Areas\UserProfiles\AdminUserProfilesController.cs" />
    <Compile Include="Areas\UserProfiles\AdminUserProfileRepresentation.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileEngagementRepresentation.cs" />
    <Compile Include="Common\AdminProjectEngagementRepresentation.cs" />
    <Compile Include="Common\Data\Events\CrudEventHandler.cs" />
    <Compile Include="Common\Data\Events\DbProjectCrudEventHandler.cs" />
    <Compile Include="Common\Data\Events\DbSharedProjectCrudEventHandler.cs" />
    <Compile Include="Common\Data\Events\ProjectModelCrudEventHandler.cs" />
    <Compile Include="Common\Auth\JwtAuthorizeFilter.cs" />
    <Compile Include="Common\Data\Events\SharedProjectModelCrudEventHandler.cs" />
    <Compile Include="Common\Data\FlagConverter.cs" />
    <Compile Include="Common\Events\RoofSnapPlatform.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\Models\SketchOrderAdHocBillingFailedItem.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\SketchOrderBillingTransactionAudit.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\Mailer\SketchOrderHandlebarsTemplate.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\Mailer\SketchOrderHtmlTemplates.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\Mailer\SketchOrderMailer.cs" />
    <Compile Include="Areas\SketchOrders\V2\BulkCreateSketchOrderV2Dto.cs" />
    <Compile Include="Areas\SketchOrders\V2\NamedSketchOrderFilter.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderProjectDefaultEdgeSubTypesApplicator.cs" />
    <Compile Include="Areas\SketchOrders\V2\SketchAdminSketchOrdersV2Controller.cs" />
    <Compile Include="Areas\SketchOrders\Dtos\V2\UpdateSketchAdminSketchOrderV2Dto.cs" />
    <Compile Include="Common\Notifications\Data\DbNotification.cs" />
    <Compile Include="Common\Notifications\Data\DbNotificationTemplate.cs" />
    <Compile Include="Common\Notifications\Data\NotificationConfiguration.cs" />
    <Compile Include="Common\Notifications\Data\NotificationTemplateConfiguration.cs" />
    <Compile Include="Common\Notifications\DTOs\NotificationDto.cs" />
    <Compile Include="Common\Notifications\DTOs\NotificationTemplateDto.cs" />
    <Compile Include="Common\Notifications\Hal\NotificationTemplateLinkTemplates.cs" />
    <Compile Include="Common\Notifications\Hal\NotificationRepresentation.cs" />
    <Compile Include="Common\Notifications\Hal\NotificationTemplateRepresentation.cs" />
    <Compile Include="Common\Notifications\NotificationTemplatesController.cs" />
    <Compile Include="Common\Notifications\NotificationProfile.cs" />
    <Compile Include="Common\Notifications\NotificationTemplateService.cs" />
    <Compile Include="Common\EndpointNotImplementedExceptionFilterAttribute.cs" />
    <Compile Include="Common\RequestExtensions.cs" />
    <Compile Include="Common\ResponseExtensions.cs" />
    <Compile Include="Common\WebAPI\HtmlActionResult.cs" />
    <Compile Include="Common\WebAPI\OrderParameter.cs" />
    <Compile Include="Areas\SketchOrders\V2\SketchOrderSortParameter.cs" />
    <Compile Include="Areas\UserProfiles\PaymentClientTokenResultRepresentation.cs" />
    <Compile Include="Areas\UserProfiles\RoofSnapPlatform.cs" />
    <Compile Include="Areas\Subscriptions\GoogleImageryDisabler.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileFactory\Decorators\AddOnUserProfileFactoryDecorator.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileFactory\Decorators\AddUserOfficeUserProfileFactoryDecorator.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileFactory\Decorators\AddUserRolesUserProfileFactoryDecorator.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileFactory\Decorators\UserLimitUserProfileFactoryDecorator.cs" />
    <Compile Include="Areas\WebHookSubscriptions\WebHookSubscriptionAuthFilter.cs" />
    <Compile Include="Areas\WebHookSubscriptions\WebHookSubscriptionController.cs" />
    <Compile Include="Areas\WebHookSubscriptions\WebHookSubscriptionDto.cs" />
    <Compile Include="Areas\WebHookSubscriptions\WebHookSubscriptionLinkTemplates.cs" />
    <Compile Include="Areas\WebHookSubscriptions\WebHookSubscriptionListRepresentation.cs" />
    <Compile Include="Areas\WebHookSubscriptions\WebHookSubscriptionProfile.cs" />
    <Compile Include="Areas\WebHookSubscriptions\WebHookSubscriptionRepresentation.cs" />
    <Compile Include="Areas\WebHookSubscriptions\WebHookSubscriptionService.cs" />
    <Compile Include="Common\AddOns\AddOnsChecker.cs" />
    <Compile Include="Common\Auth\ActiveAuthFilter.cs" />
    <Compile Include="Common\Auth\JwtOverrideAuthorize.cs" />
    <Compile Include="Common\Auth\ResourceIdAuthFilter.cs" />
    <Compile Include="Common\Auth\RoofSnapRoleChecker.cs" />
    <Compile Include="Common\Auth\RoofSnapServices.cs" />
    <Compile Include="Common\Billing\UpdatePaymentErrorRepresentation.cs" />
    <Compile Include="Common\Billing\UpdatePaymentMethodProfile.cs" />
    <Compile Include="Common\Billing\UpdatePaymentMethodRepresentation.cs" />
    <Compile Include="Common\Braintree\AddOns\AddOnManager.cs" />
    <Compile Include="Common\Braintree\AddOns\BraintreeSubscriptionUserAddOnsCounter.cs" />
    <Compile Include="Common\Braintree\AddOns\BraintreeUserAddOnManager.cs" />
    <Compile Include="Common\Braintree\AddOns\UserAddOnsRequestFactory.cs" />
    <Compile Include="App_Start\AutoMapperConfig.cs" />
    <Compile Include="App_Start\SwaggerConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateService.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\OfficePricedChargeableItemService.cs" />
    <Compile Include="Areas\Jobs\JobCloneProjectDto.cs" />
    <Compile Include="Areas\Jobs\JobResetPasswordDto.cs" />
    <Compile Include="Areas\Projects\ProjectDocuments\ProjectDocumentController.cs" />
    <Compile Include="Areas\Projects\ProjectDocuments\ProjectDocumentProfile.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\MaterialCategoryListRepresentation.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\MaterialCategoryRepresentation.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\MaterialItemListRepresentation.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\MaterialItemRepresentation.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\OfficeCategorySettingsInitializer.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\EdgeDeserializer.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\EdgeType.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Faces\FaceDeserializer.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\ProjectDrawingLinkTemplates.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Vertexes\VertexDeserializer.cs" />
    <Compile Include="Areas\Projects\ProjectProfile.cs" />
    <Compile Include="Areas\SignUp\CompletedProfile.cs" />
    <Compile Include="Areas\SketchOrders\SketchTechSketchOrderAuthFilter.cs" />
    <Compile Include="Areas\UserProfiles\InvalidUserNameException.cs" />
    <Compile Include="Areas\UserRoles\UserRoleDto.cs" />
    <Compile Include="Common\Auth\RoofSnapAuth.cs" />
    <Compile Include="Common\Auth\RoofSnapIdentityServerDiscovery.cs" />
    <Compile Include="Areas\AppVersionStatuses\AppVersionStatusProfile.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\ColorOptionProfile.cs" />
    <Compile Include="Areas\Projects\CSVExport\CSVExportProfile.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateOptionProfile.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\EstimateTemplateItemProfile.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\EstimateTemplateProfile.cs" />
    <Compile Include="Areas\Features\FeatureProfile.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementOrderProfile.cs" />
    <Compile Include="Areas\Organizations\OrganizationCreditProfile.cs" />
    <Compile Include="Areas\SignUp\SignUpProfile.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTiers\SketchOrderTierProfile.cs" />
    <Compile Include="Areas\SketchOrders\V2\SketchOrderV2Profile.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageRulesValueResolver.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTiers\TierDefinitionsValueResolver.cs" />
    <Compile Include="Areas\SketchOrders\UpdateLocationBasedDtoValueResolver.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementOrdersController.cs" />
    <Compile Include="Areas\SketchOrders\V2\SketchTechSketchOrdersV2Controller.cs" />
    <Compile Include="Areas\SketchOrders\V2\CustomerSketchOrdersV2Controller.cs" />
    <Compile Include="Areas\SketchOrders\V2\SketchOrdersV2Controller.cs" />
    <Compile Include="Areas\Organizations\OrganizationSettingConfiguration.cs" />
    <Compile Include="Areas\Organizations\DbOrganizationSetting.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTiers\SketchOrderTiersController.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\IAllowedTransitions.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\IEditableProperties.cs" />
    <Compile Include="Common\Braintree\BraintreeCustomerManager.cs" />
    <Compile Include="Common\Data\DistributedLock.cs" />
    <Compile Include="Areas\UserProfiles\CreateUserProfileDto.cs" />
    <Compile Include="Areas\Organizations\AdminOrganizationDto.cs" />
    <Compile Include="Areas\Organizations\AdminOrganizationsController.cs" />
    <Compile Include="Areas\MeasurementOrders\CreateMeasurementOrderDto.cs" />
    <Compile Include="Areas\SketchOrders\Dtos\ILocationBasedDto.cs" />
    <Compile Include="Areas\SketchOrders\Dtos\V2\UpdateSketchTechSketchOrderV2Dto.cs" />
    <Compile Include="Areas\SketchOrders\Dtos\V2\UpdateCustomerSketchOrderV2Dto.cs" />
    <Compile Include="Areas\Projects\ProjectDocuments\DocumentFailedToGenerateException.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementOrderLimitExceededException.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\NoAssociatedSketchOrderTierException.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\CannotUpdateValueException.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SharedProjectNeedsSketchTechUserIdException.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderNeedsSketchTechUserIdWhenGoingFromExceptionToInProgressException.cs" />
    <Compile Include="Areas\SketchOrders\SneakPeekUserHasExceededSketchOrderLimitException.cs" />
    <Compile Include="Common\Data\OrderingExtensions.cs" />
    <Compile Include="Common\Data\RoofSnapDataFactory.cs" />
    <Compile Include="Common\Data\Security\DataSecurityChecker.cs" />
    <Compile Include="Common\Data\Security\RoofSnapDataSecurity.cs" />
    <Compile Include="Common\Data\Security\RoofSnapDataSecurityException.cs" />
    <Compile Include="Common\Events\RoofSnapApiEventPublisher.cs" />
    <Compile Include="Common\Logging\ErrorLogger.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementOrderAuthFilter.cs" />
    <Compile Include="Common\JsonExtensions.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementOrderLinkTemplates.cs" />
    <Compile Include="Areas\SketchOrders\HAL\SketchOrderTierLinkTemplates.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\SketchTechSketchOrderV2LinkTemplates.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\CustomerSketchOrderV2LinkTemplates.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementProviders\HoverMeasurementProvider.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementProviders\IMeasurementProvider.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementProviders\IMeasurementProviderFactory.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementProviders\MeasurementProviderFactory.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementProviders\RequestMeasurementsException.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementProviders\TestMeasurementProvider.cs" />
    <Compile Include="Areas\SignUp\NaughtyNaughtyWordCensor.cs" />
    <Compile Include="Common\Data\ConcurrencyExceptions.cs" />
    <Compile Include="Areas\Passwords\PasswordController.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\PropertyEditPermissionAttribute.cs" />
    <Compile Include="Areas\Organizations\AdminOrganizationRepresentation.cs" />
    <Compile Include="Areas\Organizations\AdminOrganizationListRepresentation.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementOrderListRepresentation.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementOrderRepresentation.cs" />
    <Compile Include="Areas\Passwords\ResetPasswordHandlebarsTemplate.cs" />
    <Compile Include="Common\Mail\Mailer.cs" />
    <Compile Include="Common\Mail\RoofSnapMailMessage.cs" />
    <Compile Include="Areas\Organizations\OrganizationCreditRepresentation.cs" />
    <Compile Include="Areas\Organizations\OrganizationStatus.cs" />
    <Compile Include="Areas\SignUp\PaymentClientTokenResultDto.cs" />
    <Compile Include="Areas\SignUp\SignUpController.cs" />
    <Compile Include="Areas\WhoAmI\WhoAmIController.cs" />
    <Compile Include="Areas\Projects\SharedProjectConfiguration.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\ThreeDimensionalRoofResultsConfiguration.cs" />
    <Compile Include="Areas\Projects\DbSharedProject.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\DbThreeDimensionalRoofResults.cs" />
    <Compile Include="Areas\SignUp\CompletedProfileDto.cs" />
    <Compile Include="Areas\Passwords\PasswordResetDto.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageDto.cs" />
    <Compile Include="Areas\Passwords\RequestPasswordResetDto.cs" />
    <Compile Include="Areas\Projects\RoofImageScalarDto.cs" />
    <Compile Include="Areas\SignUp\SignupDto.cs" />
    <Compile Include="Areas\SignUp\SignupResultDto.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\UpdateEstimateItemDto.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileDto.cs" />
    <Compile Include="Areas\Nearmap\NearmapOrders\NearmapExceptions.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\EstimateTemplateSearchExtensions.cs" />
    <Compile Include="Common\Auth\AuthenticationFailureResult.cs" />
    <Compile Include="Common\Auth\JwtResult.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\DbEstimateTemplateItemFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\DbProjectEstimateItemInsertableListFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateOptionLimitExceededException.cs" />
    <Compile Include="Areas\SignUp\CompleteSignUpAuthFilter.cs" />
    <Compile Include="Areas\Features\FeatureTemplates.cs" />
    <Compile Include="Areas\SignUp\SignUpHandlebarsTemplate.cs" />
    <Compile Include="Areas\SignUp\SignUpHtmlTemplates.cs" />
    <Compile Include="Areas\Passwords\ResetPasswordHtmlTemplates.cs" />
    <Compile Include="Areas\SignUp\NewSignupMailer.cs" />
    <Compile Include="Areas\Passwords\ResetPasswordInfo.cs" />
    <Compile Include="Areas\Passwords\ResetPasswordMailer.cs" />
    <Compile Include="Areas\SignUp\SignupInfo.cs" />
    <Compile Include="Areas\Projects\ProjectDocuments\ProjectDocumentCategory.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\OrganizationFeaturesFactory.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\SearchMaterialItemProfile.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\MaterialCategoryProfile.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentProfile.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\DocumentTemplateDataProfile.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\DocumentTemplateProfile.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\MaterialItemImageUrlFactory.cs" />
    <Compile Include="Areas\Nearmap\NearmapConfig\NearMapConfigProfile.cs" />
    <Compile Include="Areas\Nearmap\NearmapOrders\NearmapOrderProfile.cs" />
    <Compile Include="Areas\Offices\OfficeDocuments\OfficeDocumentProfile.cs" />
    <Compile Include="Areas\Offices\OfficeProfile.cs" />
    <Compile Include="Areas\Nearmap\NearmapTokens\NearmapTokenProfile.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Measurements\ProjectMeasurementsProfile.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\ThreeDimensionalProjectDrawingProfile.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTransactionUserSummaryItemProfile.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\EdgeRepresentationFactory.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Faces\FaceRepresentationFactory.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Vertexes\PinRepresentationFactory.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Vertexes\VertexRepresentationFactory.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\DbEdgeFactory.cs" />
    <Compile Include="Areas\Organizations\OrganizationProfile.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Faces\DbFaceEdgeTypeFactory.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Faces\DbFaceFactory.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\ProjectDrawingProfile.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\DbToRepresentationProjectDrawingTypeConverter.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageProfile.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\ColorOptionImageToColorOptionRepresentationImageResolver.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\DbMaterialSearchItemImageToMaterialSearchItemRepresentationImageResolver.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ImageUrlValueResolver.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\ProjectDrawingDtoToDbProjectDrawingTypeConverter.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderProfile.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileProfile.cs" />
    <Compile Include="Areas\UserRoles\UserRoleProfile.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Vertexes\VertexProfile.cs" />
    <Compile Include="Common\Config\Config.cs" />
    <Compile Include="Common\Config\IConfig.cs" />
    <Compile Include="Areas\AppVersionStatuses\AppVersionStatusesController.cs" />
    <Compile Include="Areas\BraintreeWebhooks\BraintreeWebhooksController.cs" />
    <Compile Include="Common\Auth\ControllerExtensions.cs" />
    <Compile Include="Areas\ErrorTest\ErrorTestController.cs" />
    <Compile Include="Areas\Features\FeaturesController.cs" />
    <Compile Include="Common\ServiceResult.cs" />
    <Compile Include="Common\WebAPI\ICurrentEtag.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\ChargeableItemColorConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\MaterialSearchItemConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\DbMaterialSearchItem.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\DbChargeableItemColor.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\ThreeDimensionalProjectDrawing.cs" />
    <Compile Include="Common\Braintree\BraintreeGatewayFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\CreateEstimateItemStrategyFactory.cs" />
    <Compile Include="Common\Http\CookielessFlurlHttpClientFactory.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\ThreeDimensionalProjectDrawingLinkTemplates.cs" />
    <Compile Include="Areas\BraintreeWebhooks\BraintreeWebhookFormData.cs" />
    <Compile Include="Areas\SignUp\CompletedProfileResultDto.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatureRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectImages\CanDeleteProjectImageRule.cs" />
    <Compile Include="Areas\Projects\ProjectImages\IProjectImageRule.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageCannotDeleteException.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageKeys.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageModelRuleEvaluator.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageRuleNameRepresentation.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\ThreeDimensionalProjectDrawingRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageDataListRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTiers\SketchOrderTierDefinition.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTiers\SketchOrderTierDefinitionRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTiers\SketchOrderTierListRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderTiers\SketchOrderTierRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\SketchTechSketchOrderListV2Representation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\SketchOrdersTotalSummaryRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\CustomerSketchOrderListV2Representation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\V2\CustomerSketchOrderV2Representation.cs" />
    <Compile Include="Areas\Organizations\ResourceType.cs" />
    <Compile Include="Common\Data\RoofSnapRepository.cs" />
    <Compile Include="Common\Braintree\RoofSnapSubscriptionPlans.cs" />
    <Compile Include="Areas\BraintreeWebhooks\BraintreeSubscriptionService.cs" />
    <Compile Include="Areas\Offices\CannotDeleteLastOfficeException.cs" />
    <Compile Include="Areas\UserProfiles\CannotRemoveLastUserOfficeException.cs" />
    <Compile Include="Areas\Organizations\CrossOrganizationSecurityException.cs" />
    <Compile Include="Areas\Organizations\OrganizationNotFoundException.cs" />
    <Compile Include="Areas\Projects\ProjectDocuments\DocumentGenerator.cs" />
    <Compile Include="Areas\MeasurementOrders\MeasurementOrdersService.cs" />
    <Compile Include="Areas\Organizations\OrganizationCreditCalculator.cs" />
    <Compile Include="Areas\Organizations\OrganizationService.CoreData.cs" />
    <Compile Include="Areas\Organizations\OrganizationSettingsChecker.cs" />
    <Compile Include="Areas\Projects\ProjectService.CoreData.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\CreateEstimateCustomItemStrategy.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\CreateEstimateItemStrategy.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateTotalCalculators\EstimateTotalCalculatorItemFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateTotalCalculators\EstimateMarkupDiscountCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateTotalCalculators\EstimateMaterialCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateTotalCalculators\EstimateSubtotalCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateTotalCalculators\EstimateTaxCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateTotalCalculators\Models\EstimateTotalCalculatorItem.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateTotalCalculators\Models\EstimateTotalCalculatorOffice.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateTotalCalculators\Models\EstimateTotalCalculatorSubMaterialItem.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateTotalCalculators\EstimateTotalCalculatorOfficeFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\OfficePricedChargeableItemsUnitsCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemRules\CanDeleteProjectEstimateItemRule.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemRules\ProjectEstimateItemRuleEvaluator.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemRules\IProjectEstimateItemRule.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateTotalCalculators\ItemTypeArrayFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateTotalCalculators\TotalByItemTypeCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemRules\ProjectEstimateItemCannotDeleteException.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemRules\ProjectEstimateItemRuleName.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\SquaresCalculators\SteepRoofChargeUnitQuantityCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\SquaresCalculators\TwoStoryRoofChargeUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Estimating\StaticCategoryIds.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\CreateEstimateItemDto.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\DocumentTemplatesController.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\DbEstimateTemplateConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\EstimateTemplateItemConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\DbEstimateTemplate.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\DbEstimateTemplateItem.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\CreateEstimateTemplateDto.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\UpdateEstimateDto.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\InvalidMaterialItemIdException.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\ProjectDrawing\ProjectEstimateItemFromPinFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\ProjectDrawing\ProjectEstimateItemsFromDrawingFacetAttributesFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\ProjectDrawing\ProjectEstimateItemsFromDrawingPinsFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\ProjectDrawing\ProjectEstimateItemFromPitchValueFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\ProjectDrawing\ProjectEstimateItemFromTwoStoryFacetAttributeFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\ProjectEstimateChooser.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\ProjectDrawing\ProjectEstimateItemsFromFacetPitchValuesFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\ProjectDrawing\ProjectEstimateItemsFromProjectDrawingFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\ProjectDrawing\ProjectEstimateItemsFromTwoStoryFacetsFactory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateTotalCalculators\EstimateTotalCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\UniqueMaterialItemsPerEstimateChecker.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\ProjectEstimateItemInsertableFactory.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\EstimateTemplateAuthFilter.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\EstimateTemplateItemLinkTemplates.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\SearchMaterialItemLinkTemplates.cs" />
    <Compile Include="Areas\Features\FeaturesModel.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\ColorOptionListRepresentation.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\ColorOptionRepresentation.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\EstimateTemplateItemRepresentation.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\EstimateTemplateRepresentation.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\EstimateTemplateListRepresentation.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\EstimateTemplatesController.cs" />
    <Compile Include="Areas\Offices\OfficesController.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\CategoryConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ChargeableItemConfiguration.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\DocumentTemplateConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\OfficeCategorySettingConfiguration.cs" />
    <Compile Include="Areas\Offices\OfficeDocuments\OfficeDocumentConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\OfficeHiddenImageConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\OfficePricedChargeableItemColorOptionConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\OfficePricedChargeableItemInsertableConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\OfficePricedChargeableItemConfiguration.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemConfiguration.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\CreateEstimateItem\ProjectEstimateItemInsertableConfiguration.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\ProjectEstimateOptionConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\SubMaterialRelationshipsConfiguration.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\DbCategory.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\DbChargeableItem.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\DbDocumentTemplate.cs" />
    <Compile Include="Areas\Estimating\DbItemType.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\DbOfficeCategorySetting.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\DbOfficeCustomCategory.cs" />
    <Compile Include="Areas\Offices\DbOfficeDocument.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\DbOfficeHiddenImage.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\DbOfficePriceChargeableItemColorOption.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\DbProjectEstimateItem.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\DbOfficePricedChargeableItemInsertable.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\DbProjectEstimateItemInsertable.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\DbProjectEstimateOption.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\DbSubMaterialRelationships.cs" />
    <Compile Include="Areas\Estimating\DbUnitType.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\ColorOption.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\DbOfficePricedChargeableItem.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\MaterialCategory.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\CreateEstimateOptionDto.cs" />
    <Compile Include="Areas\Projects\ProjectImages\PinImageDto.cs" />
    <Compile Include="Areas\Projects\ProjectImages\SketchReportPinsImageDto.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\CreateDocumentTemplateDto.cs" />
    <Compile Include="Areas\CustomDocuments\UpdateDocumentDto.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\UpdateDocumentTemplateDto.cs" />
    <Compile Include="Areas\Projects\DeleteProjectFilter.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentAuthFilter.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\DocumentTemplateAuthFilter.cs" />
    <Compile Include="Areas\Offices\OfficeAuthFilter.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateLinkTemplates.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\EstimateTemplateLinkTemplates.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\MaterialCategoryLinkTemplates.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\ColorOptions\ColorOptionLinkTemplates.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\MaterialItemLinkTemplates.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\DocumentTemplateLinkTemplates.cs" />
    <Compile Include="Areas\Offices\OfficeDocuments\OfficeDocumentLinkTemplates.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateListRepresentation.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateOptions\EstimateRepresentation.cs" />
    <Compile Include="Areas\Estimating\ItemType.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\SearchMaterialItemListRepresentation.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\SearchMaterialItemRepresentation.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentListRepresentation.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\DocumentTemplateListRepresentation.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\DocumentTemplateRepresentation.cs" />
    <Compile Include="Areas\Offices\OfficeDocuments\OfficeDocumentListRepresentation.cs" />
    <Compile Include="Areas\Offices\OfficeDocuments\OfficeDocumentRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageDataRepresentation.cs" />
    <Compile Include="Areas\Estimating\UnitType.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\OfficePricedChargeableItemCategoryResolver.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\OfficePricedChargeableItemParentCategoryResolver.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\IProjectEstimateItemInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemOfficePricedChargeableItemInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemColorIdInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemCoveragePerUnitInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemDescriptionInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemEstimateCategoryIdInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemHideOnContractInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemHideOnEstimateInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemHideOnMaterialOrderInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemImageInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemItemTypeInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemLaborCostInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemMaterialCostInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemMaterialOrderDescriptionInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemOrderInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemSystemChargeableItemIdInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemTotalPerUnitInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\EstimateItems\ProjectEstimateItemInitializers\ProjectEstimateItemUnitsInitializer.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\IUnitQuantityFromMeasurementsCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\IMeasurementFromMeasurementCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\IUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\SquaresCalculators\LowSlopeRoofingUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\SquaresCalculators\ISquareTypeUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\SquaresCalculators\RemoveAndDisposeSecondLayerUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\SquaresCalculators\RemoveAndDisposeShinglesUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\SquaresCalculators\ShingleFastenersUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\SquaresCalculators\UnderlaymentUnitQuantityFromCategoryCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\ActualSquaresUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\ApronFlashingUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\DownSpoutsQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\EaveEdgeUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\EavesUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\GuttersUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\HipsUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\IceWaterShieldUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\LowSlopeUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\PitchChangeUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\RakeEdgeUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\RakesUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\RidgesUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\RidgeVentUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\SecondLayerUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\StepFlashingUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\StepUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\TotalSquaresFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\TwoStoryUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\UnitQuantityFromMeasurementsCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\ValleysUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromMeasurementCalculators\WallUnitQuantityFromMeasurmentCalculator.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\WastePercentageCalculator.cs" />
    <Compile Include="Areas\Organizations\OrganizationFeatures\OrganizationFeature.cs" />
    <Compile Include="Areas\Projects\CSVExport\CSVRequiredModelsModel.cs" />
    <Compile Include="Areas\Projects\CSVExport\ExportCSVModel.cs" />
    <Compile Include="Areas\SketchOrders\GeocodingService.cs" />
    <Compile Include="Areas\Passwords\PasswordService.cs" />
    <Compile Include="Areas\Passwords\SendResetPasswordEmail.cs" />
    <Compile Include="Areas\Projects\ProjectCloner.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrdersByOrganizationQueryParams.cs" />
    <Compile Include="Common\Mail\FileReaderBase.cs" />
    <Compile Include="Areas\SignUp\RandomStringGeneratorService.cs" />
    <Compile Include="Areas\Nearmap\NearmapOrders\RoofSnapCreditTransactionator.cs" />
    <Compile Include="Areas\SignUp\SignUpService.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderCostCalculator.cs" />
    <Compile Include="Common\Data\UnitOfWork.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileService.CoreData.cs" />
    <Compile Include="Areas\UserProfiles\ActiveUserCountExceededException.cs" />
    <Compile Include="Areas\Organizations\ActiveUsersCounter.cs" />
    <Compile Include="Common\Braintree\AddOns\FailedAddingBraintreeAddOnException.cs" />
    <Compile Include="Areas\UserProfiles\UserLimitChecker.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileFactory\UserProfileFactory.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileFactory\UserProfileFactoryDecorator.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileFactory\UserProfileFactoryDecoratorIoCModule.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderContexts\SketchOrderContextAccessMode.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\ExceptionSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\PendingRejectionSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\QueuedSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\BilledSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\CancelledSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\CompleteSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\EntryValueResolver.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\IncompleteSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\InProgressSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\PendingReviewSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\PendingSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderContexts\SketchOrderContext.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderContexts\ISketchOrderContextFactory.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SharedProjectFactory.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderProjectCloningHandler.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderResponseSender.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchOrderStates\SketchOrderState.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\UpdateSketchOrderPropertyChecker.cs" />
    <Compile Include="Common\StringExtensions.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Calculators\RelativeAltitudeCalculator.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Calculators\TwoStoryAltitudeCalculator.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\ConnectedFacetAltitudeCalculator.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\DrawingRelationships\ConnectedFaces.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\DrawingRelationships\FaceCategories.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Factories\FaceCategoriesFactory.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Calculators\DirectionalDeltaConversionCalculator.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Calculators\NextFacetTraversingDownAnalysisCalculator.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Factories\RelativeAltitudesListFactory.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Calculators\VerticalTranslationCalculator.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\DrawingRelationships\ConnectedBaseFacets.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Factories\FaceFactory.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\DrawingRelationships\ParallelEdges.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\ThreeDimensionalConversionProfile.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\DrawingRelationships\Face.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Calculators\CounterClockwiseOrderTestCalculator.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Calculators\VertexesInCounterClockwiseTestCalculator.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\DrawingRelationships\Edge.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Factories\FacesFactory.cs" />
    <Compile Include="Areas\CustomDocuments\CustomImageTagProcessor.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentService.cs" />
    <Compile Include="Areas\CustomDocuments\CreateDocumentDto.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentsController.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\DocumentTemplateData.cs" />
    <Compile Include="Areas\CustomDocuments\InputProcessors\CheckBoxInputProcessor.cs" />
    <Compile Include="Areas\CustomDocuments\InputProcessors\IInputProcessor.cs" />
    <Compile Include="Areas\CustomDocuments\InputProcessors\InputProcessor.cs" />
    <Compile Include="Areas\CustomDocuments\InputProcessors\SelectInputProcessor.cs" />
    <Compile Include="Areas\CustomDocuments\InputProcessors\TextAreaInputProcessor.cs" />
    <Compile Include="Areas\CustomDocuments\InputProcessors\TextInputProcessor.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentTemplates\DocumentTemplateService.cs" />
    <Compile Include="Areas\Offices\Estimating\Templates\EstimateTemplateService.cs" />
    <Compile Include="Areas\Features\FeaturesService.cs" />
    <Compile Include="Areas\Jobs\JobsController.cs" />
    <Compile Include="Areas\Nearmap\NearmapConfig\NearmapConfigController.cs" />
    <Compile Include="Areas\Nearmap\NearmapOrders\NearmapOrdersController.cs" />
    <Compile Include="Areas\Nearmap\NearmapTokens\NearmapTokensController.cs" />
    <Compile Include="Areas\UserRoles\UserRolesController.cs" />
    <Compile Include="Areas\AppVersionStatuses\AppVersionStatusConfiguration.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentConfiguration.cs" />
    <Compile Include="Areas\Offices\OfficeConfiguration.cs" />
    <Compile Include="Areas\Nearmap\NearmapTokens\NearmapTokenConfiguration.cs" />
    <Compile Include="Areas\AppVersionStatuses\DbAppVersionStatus.cs" />
    <Compile Include="Areas\CustomDocuments\DbDocument.cs" />
    <Compile Include="Areas\Offices\DbOffice.cs" />
    <Compile Include="Areas\Nearmap\NearmapTokens\DbNearmapToken.cs" />
    <Compile Include="Areas\AppVersionStatuses\AppVersionStatusDto.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\CombinedProjectDrawing.cs" />
    <Compile Include="Areas\Projects\ProjectImages\AreaImageDto.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\EdgeTypeDto.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Measurements\MeasurementsDto.cs" />
    <Compile Include="Areas\Nearmap\NearmapOrders\NearmapOrderDto.cs" />
    <Compile Include="Areas\Projects\ProjectImages\MeasurementsImageDto.cs" />
    <Compile Include="Areas\Projects\ProjectImages\PitchImageDto.cs" />
    <Compile Include="Areas\Projects\ProjectImages\RoofImageDto.cs" />
    <Compile Include="Areas\Jobs\UpdateMultiProjectOwnerDto.cs" />
    <Compile Include="Areas\Projects\CurrentUsernameDoesNotHaveProjectsException.cs" />
    <Compile Include="Common\BlobStorage\BlobStorageFactory.cs" />
    <Compile Include="Common\Data\DbContextFactory.cs" />
    <Compile Include="Areas\Nearmap\NearmapOrders\NearmapOrdersAuthFilter.cs" />
    <Compile Include="Areas\Organizations\OrganizationAuthFilter.cs" />
    <Compile Include="Areas\Projects\ProjectAuthFilter.cs" />
    <Compile Include="Common\Auth\RouteDataParser.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileAuthFilter.cs" />
    <Compile Include="Common\WebAPI\QueryableOrderer.cs" />
    <Compile Include="Common\WebAPI\SortParameter.cs" />
    <Compile Include="Common\WebAPI\SortParameterFactory.cs" />
    <Compile Include="CustomLogzioTarget.cs" />
    <Compile Include="ErrorHandler\AiHandleErrorAttribute.cs" />
    <Compile Include="FilterConfig.cs" />
    <Compile Include="IoC\ConventionsModule.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentLinkTemplates.cs" />
    <Compile Include="Areas\Nearmap\NearmapConfig\NearmapConfigLinkTemplates.cs" />
    <Compile Include="Areas\Nearmap\NearmapOrders\NearmapOrderLinkTemplates.cs" />
    <Compile Include="Areas\Nearmap\NearmapTokens\NearmapTokenLinkTemplates.cs" />
    <Compile Include="Areas\Offices\OfficeLinkTemplates.cs" />
    <Compile Include="Areas\AppVersionStatuses\AppVersionStatusLinkTemplates.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageLinktemplates.cs" />
    <Compile Include="Areas\UserRoles\UserRoleLinkTemplates.cs" />
    <Compile Include="Areas\CustomDocuments\DocumentRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\EdgeTypeRepresentation.cs" />
    <Compile Include="Areas\AppVersionStatuses\AppVersionStatusListRepresentation.cs" />
    <Compile Include="Areas\AppVersionStatuses\AppVersionStatusRepresentation.cs" />
    <Compile Include="Areas\Jobs\JobResultModel.cs" />
    <Compile Include="Areas\Nearmap\NearmapConfig\NearMapConfigRepresentation.cs" />
    <Compile Include="Areas\Nearmap\NearmapOrders\NearmapOrderRepresentation.cs" />
    <Compile Include="Areas\Offices\OfficeListRepresentation.cs" />
    <Compile Include="Areas\Offices\OfficeRepresentation.cs" />
    <Compile Include="Areas\Organizations\EmbeddedOrganizationRepresentation.cs" />
    <Compile Include="Areas\Nearmap\NearmapTokens\NearMapTokenRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Measurements\Measurements.cs" />
    <Compile Include="Areas\Nearmap\NearmapOrders\NearmapOrderListRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageRepresentation.cs" />
    <Compile Include="Areas\UserRoles\UserRoleListRepresentation.cs" />
    <Compile Include="Areas\UserRoles\UserRoleRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ByteArrayImage.cs" />
    <Compile Include="Areas\AppVersionStatuses\AppVersionStatusService.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ImageConverterService.cs" />
    <Compile Include="Common\WebAPI\RoofSnapApiController.cs" />
    <Compile Include="Areas\Projects\ProjectDocuments\ProjectDocumentConfiguration.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageConfiguration.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Measurements\ProjectMeasurementsConfiguration.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\ProjectDrawingConfiguration.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\DbEdge.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Faces\DbFace.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Faces\DbFaceEdge.cs" />
    <Compile Include="Areas\Projects\ProjectDocuments\DbProjectDocument.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\DbProjectDrawing.cs" />
    <Compile Include="Areas\Projects\ProjectImages\DbProjectImage.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Measurements\DbProjectMeasurements.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Vertexes\DbVertex.cs" />
    <Compile Include="Common\Data\IVersioned.cs" />
    <Compile Include="Areas\Projects\Coordinate.cs" />
    <Compile Include="Areas\Projects\CreateProjectDto.cs" />
    <Compile Include="Areas\Projects\ProjectImages\CreateProjectImageDto.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\EdgeDto.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\EdgeSubTypeDto.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Vertexes\PinDto.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\EdgeSubTypeRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Faces\FaceDto.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Faces\FaceTypeDto.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\ProjectDrawingDto.cs" />
    <Compile Include="Areas\Projects\ProjectDocuments\ProjectDocumentDto.cs" />
    <Compile Include="Areas\Projects\UpdateProjectDto.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Vertexes\VertexDto.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSWorkflow\SketchTechRequiredException.cs" />
    <Compile Include="Areas\Projects\ProjectDocuments\ProjectDocumentLinkTemplates.cs" />
    <Compile Include="Areas\Projects\ProjectLinkTemplates.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Edges\EdgeRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Faces\FaceRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Faces\FaceTypeRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Vertexes\PinRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectDocuments\ProjectDocumentListRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectDocuments\ProjectDocumentRepresentation.cs" />
    <Compile Include="Common\HAL\UnpagedRepresentationList.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\ProjectDrawingRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\Vertexes\VertexRepresentation.cs" />
    <Compile Include="Areas\Offices\IOfficeService.cs" />
    <Compile Include="Areas\Nearmap\NearmapConfig\NearMapConfigService.cs" />
    <Compile Include="Areas\Nearmap\NearmapOrders\NearmapOrderService.cs" />
    <Compile Include="Areas\Nearmap\NearmapTokens\NearmapTokenService.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialCategories\OfficeCategorySettingsFactory.cs" />
    <Compile Include="Areas\Offices\OfficeDocuments\OfficeDocumentService.cs" />
    <Compile Include="Areas\Offices\Estimating\MaterialItems\OfficePricedChargeableItemQueries.cs" />
    <Compile Include="Areas\Offices\OfficeService.cs" />
    <Compile Include="Common\Data\PagingExtensionsCore.cs" />
    <Compile Include="Areas\Projects\ProjectDocuments\ProjectDocumentService.cs" />
    <Compile Include="Common\Data\PagingExtensions.cs" />
    <Compile Include="Areas\Projects\ProjectListRepresentation.cs" />
    <Compile Include="Areas\Projects\ProjectRepresentation.cs" />
    <Compile Include="Areas\Projects\IProjectService.cs" />
    <Compile Include="Common\WebAPI\PagingInfo.cs" />
    <Compile Include="Areas\Projects\ProjectsController.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrdersController.cs" />
    <Compile Include="Areas\Projects\ProjectConfiguration.cs" />
    <Compile Include="Areas\Projects\DbProject.cs" />
    <Compile Include="Areas\SketchOrders\Dtos\SketchReportTypeDto.cs" />
    <Compile Include="Areas\SketchOrders\Dtos\UpdateSketchOrderDto.cs" />
    <Compile Include="Areas\SketchOrders\InvalidSketchOrderStatusTransitionException.cs" />
    <Compile Include="Common\Auth\ApiKeyAuthorizeFilter.cs" />
    <Compile Include="Common\Auth\JwtAuthenticationFilter.cs" />
    <Compile Include="Common\HAL\LinkFactory.cs" />
    <Compile Include="Areas\SketchOrders\HAL\SketchOrderLinkTemplates.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileLinkTemplates.cs" />
    <Compile Include="Common\HAL\DefaultRelRepresentation.cs" />
    <Compile Include="Common\HAL\PagedRepresentationList.cs" />
    <Compile Include="Areas\Organizations\OrganizationListRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\SketchOrderListRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\SketchOrderRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\HAL\SketchReportTypeRepresentation.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileListRepresentation.cs" />
    <Compile Include="Areas\UserProfiles\IUserProfileService.cs" />
    <Compile Include="Areas\Organizations\OrganizationsController.cs" />
    <Compile Include="Areas\UserProfiles\UserProfilesController.cs" />
    <Compile Include="Common\HAL\IVersionedRepresentation.cs" />
    <Compile Include="Areas\SketchOrders\Dtos\CreateSketchOrderDto.cs" />
    <Compile Include="Common\Data\ConcurrentAttribute.cs" />
    <Compile Include="Common\Data\OptimisticConcurrencyExceptionAttribute.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="IoC\DefaultModule.cs" />
    <Compile Include="Common\Data\IRoofSnapLiveDbContext.cs" />
    <Compile Include="Areas\Organizations\OrganizationLinkTemplates.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSMailer\SketchOSMailer.cs" />
    <Compile Include="Areas\Organizations\OrganizationConfiguration.cs" />
    <Compile Include="Areas\SketchOrders\Data\SketchOrderConfiguration.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileConfiguration.cs" />
    <Compile Include="Areas\UserRoles\UserRoleConfiguration.cs" />
    <Compile Include="Areas\Organizations\DbOrganization.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbSketchOrder.cs" />
    <Compile Include="Areas\SketchOrders\Data\SketchOrderStatusHumanFriendlyStrings.cs" />
    <Compile Include="Areas\SketchOrders\Data\DbSketchReportType.cs" />
    <Compile Include="Areas\UserProfiles\DbUserProfile.cs" />
    <Compile Include="Areas\UserRoles\DbUserRole.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Areas\Organizations\OrganizationRepresentation.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileRepresentation.cs" />
    <Compile Include="Areas\Organizations\IOrganizationService.cs" />
    <Compile Include="Areas\Organizations\OrganizationService.cs" />
    <Compile Include="Areas\Projects\ProjectDrawings\ProjectDrawingPinExtractor.cs" />
    <Compile Include="Areas\Projects\ProjectFactory.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageServiceModel.cs" />
    <Compile Include="Areas\Projects\ProjectImages\ProjectImageService.cs" />
    <Compile Include="Areas\Projects\ProjectService.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderService.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\UnitQuantityFromOfficePricedChargeableItemsAndProjectMeasurementsCalculator.cs" />
    <Compile Include="Areas\Projects\Estimating\UnitQuantityCalculation\TotalSquaresCalculator.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Calculators\VertexAltitudeCalculator.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\DrawingRelationships\ConnectedPath.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\DrawingRelationships\ConnectedEdges.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Factories\ThreeDimensionalProjectDrawingFactory.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\ThreeDimensionalService.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\Factories\VertexesAtEachParallelDeltaListFactory.cs" />
    <Compile Include="Areas\UserProfiles\UserProfileService.cs" />
    <Compile Include="Areas\UserRoles\UserRoleService.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\Models\SketchOrderTransactionError.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\Models\SketchOrderTransactionSummary.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\Models\SketchOrderTransactionUserSummaryItem.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\OrganizationSketchOrderBillingQueryFactory.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\SketchOrderTransactionSummaryBuilder.cs" />
    <Compile Include="Areas\SketchOrders\SketchServiceBilling\OrganizationSketchOrderBillingService.cs" />
    <Compile Include="Areas\Projects\ThreeDimensionConversion\DrawingRelationships\Vertex.cs" />
    <Compile Include="Areas\UserRoles\RoofSnapRoles.cs" />
    <Compile Include="Common\Billing\CreditCardDeclinedException.cs" />
    <Compile Include="Common\Billing\CreditCardException.cs" />
    <Compile Include="Common\Billing\CreditCardNotVerifiedException.cs" />
    <Compile Include="Common\Billing\CreditCardUpdateException.cs" />
    <Compile Include="Common\Braintree\CustomerRequestFactory.cs" />
    <Compile Include="Areas\Subscriptions\ExistingCustomerActiveSubscriptionUpdateStrategy.cs" />
    <Compile Include="Areas\Subscriptions\ExistingCustomerCancelledSubscriptionUpdateStrategy.cs" />
    <Compile Include="Areas\Subscriptions\ExistingCustomerPastDueSubscriptionUpdateStrategy.cs" />
    <Compile Include="Areas\UserProfiles\PaymentMethods\ExistingCustomerPaymentMethodUpdateStrategy.cs" />
    <Compile Include="Areas\UserProfiles\PaymentMethods\IPaymentMethodUpdateStrategy.cs" />
    <Compile Include="Areas\Subscriptions\ISubscriptionUpdateStrategy.cs" />
    <Compile Include="Common\Billing\IUpdatePaymentErrorModelFactory.cs" />
    <Compile Include="Areas\UserProfiles\PaymentMethods\NewSubscriptionPaymentMethodUpdateStrategy.cs" />
    <Compile Include="Areas\Subscriptions\NewCustomerSubscriptionUpdateStrategy.cs" />
    <Compile Include="Areas\UserProfiles\PaymentMethods\PaymentMethodUpdateStrategyFactory.cs" />
    <Compile Include="Areas\Subscriptions\SubscriptionService.cs" />
    <Compile Include="Areas\Subscriptions\NewSubscriptionRequestFactory.cs" />
    <Compile Include="Areas\Subscriptions\SubscriptionUpdateResult.cs" />
    <Compile Include="Areas\Subscriptions\SubscriptionUpdateResultFactory.cs" />
    <Compile Include="Areas\Subscriptions\SubscriptionUpdateStrategyFactory.cs" />
    <Compile Include="Common\Billing\UpdatePaymentErrorModel.cs" />
    <Compile Include="Common\Billing\UpdatePaymentErrorModelFactory.cs" />
    <Compile Include="Common\Billing\UpdatePaymentErrorType.cs" />
    <Compile Include="Common\Billing\UpdatePaymentMethodDto.cs" />
    <Compile Include="Common\Billing\UpdatePaymentMethodResult.cs" />
    <Compile Include="Areas\UserProfiles\PaymentMethods\UserProfilePaymentService.cs" />
    <Compile Include="Areas\UserRoles\UserRoleStrings.cs" />
    <Compile Include="Common\Wrappers\BlobStorageReadWrapper.cs" />
    <Compile Include="Common\Wrappers\BlobStorageWrapper.cs" />
    <Compile Include="Common\Wrappers\BlobStorageWriteWrapper.cs" />
    <Compile Include="Common\Wrappers\DateTimeOffsetWrapper.cs" />
    <Compile Include="Common\Wrappers\DateTimeWrapper.cs" />
    <Compile Include="Common\Wrappers\FileWrapper.cs" />
    <Compile Include="Common\Wrappers\GuidWrapper.cs" />
    <Compile Include="Common\Wrappers\JsonConvertWrapper.cs" />
    <Compile Include="Common\Auth\RolesWrapper.cs" />
    <Compile Include="Common\Auth\WebSecurityWrapper.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="CircularReferenceRemovalActionFilter.cs" />
    <Compile Include="Areas\SketchOrders\SketchOSMailer\SketchOrderEmail.cs" />
    <Compile Include="SqlServerTypes\Loader.cs" />
    <Compile Include="Web References\com.salesforce.my.roofsnap\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Content Include="Areas\Documents\GlobalTemplateHandlebars\Partials\__blankSignatureBlock.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Documents\GlobalTemplateHandlebars\MetalRoofReport.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="Areas\Documents\GlobalTemplateHandlebars\SketchOrderReportContinentalMapping.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Areas\Documents\GlobalTemplateHandlebars\Partials\__signatureBlock.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <Content Include="Areas\SignUp\NewSignupEmail.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Passwords\ResetPassword.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Compile Include="Areas\SnapEstimates\EstimateType.cs" />
    <Content Include="parameters.xml">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="RoofSnap.WebAPI.ruleset" />
    <Content Include="NLog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Areas\SketchOrders\SketchServiceBilling\Mailer\Handlebars\SketchOSAdHocBillingFailedEmail.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Compile Include="Common\Data\RoofSnapLiveDbContext.cs" />
    <Content Include="Areas\SketchOrders\SketchServiceBilling\Mailer\Handlebars\SketchOSAdminSuccessSummaryItem.partial.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\readme.htm" />
    <Content Include="SqlServerTypes\x64\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x64\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Static\baseline_error_outline_black_48dp.png" />
    <Content Include="Static\checkmark.jpg" />
    <Content Include="Static\CKEditor.css" />
    <Content Include="Static\estimate-background.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Static\normalize.min.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Static\roofsnap-footer-logo-new.svg" />
    <Content Include="Static\roofsnap-footer-logo-white-new.svg" />
    <Content Include="Static\roofsnap-footer-logo-white.svg" />
    <Content Include="Static\roofsnap-footer-logo.svg" />
    <Content Include="Static\server-started.htm" />
    <Content Include="Static\signature_pad.umd.min.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Static\sketch-ordering-service-logo.svg" />
    <Content Include="Static\sketch-ordering-service-white-logo.svg" />
    <Content Include="Areas\SketchOrders\SketchServiceBilling\Mailer\Handlebars\SketchOSError.partial.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\SketchOrders\SketchServiceBilling\Mailer\Handlebars\SketchOSUserOrderSummaryEmail.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\SketchOrders\SketchServiceBilling\Mailer\Handlebars\SketchOSUserTransaction.partial.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\SketchOrders\SketchServiceBilling\Mailer\Handlebars\SketchOSAdminSummary.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\SketchOrders\SketchServiceBilling\Mailer\Handlebars\SketchOSAdminErrorSummaryItem.partial.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Compile Include="Areas\Offices\OfficeService.CoreData.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Global.asax" />
    <Content Include="Areas\SketchOrders\SketchServiceBilling\Mailer\Handlebars\SketchOrderRefund.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Documents\GlobalTemplateHandlebars\SketchReport.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Documents\GlobalTemplateHandlebars\EstimateReport.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="ApplicationInsights.config" />
    <Content Include="Areas\Documents\GlobalTemplateHandlebars\MultiEstimateReport.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Documents\GlobalTemplateHandlebars\MaterialOrderReport.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Documents\GlobalTemplateHandlebars\SummaryReport.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Documents\GlobalTemplateHandlebars\LaborReport.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Billing\Receipts\TransactionReceipt.hbs">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Billing\Receipts\CancellationReceipt\CancellationReceipt.hbs">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Billing\Receipts\SubscriptionBillingFailed\SubscriptionBillingFailed.hbs">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\SignUp\Registration\CompleteRegistration.hbs">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Billing\Receipts\DowngradeReceipt\DowngradeReceipt.hbs">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Salesforce\README.md" />
    <Content Include="Areas\Nearmap\Billing\Mail\Handlebars\AdminOrderSummaryEmail.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Nearmap\Billing\Mail\Handlebars\AdminTransaction.partial.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Nearmap\Billing\Mail\Handlebars\Error.partial.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Nearmap\Billing\Mail\Handlebars\UserOrderSummaryEmail.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Nearmap\Billing\Mail\Handlebars\UserTransaction.partial.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Documents\GlobalTemplateHandlebars\MarketSharpSketchReport.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Documents\GlobalTemplateHandlebars\GutterReport.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="Areas\Documents\GlobalTemplateHandlebars\SketchReportClassic.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <Content Include="Areas\SketchOrders\SketchServiceBilling\Mailer\Handlebars\SketchOSOrganizationBillingFailedEmail.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Nearmap\Billing\Mail\Handlebars\NearmapBillingTransactionFailedEmail.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\SketchOrders\SketchOSMailer\GutterReportCompleteEmail.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\SketchOrders\SketchOSMailer\SketchOSIncompleteEmail.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Estimating\EstimatesMailer\EstimatedMaterial.hbs">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\SketchOrders\SketchOSMailer\SketchOSCompleteEmail.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\SketchOrders\SketchOSMailer\MetalRoofReportCompleteEmail.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Documents\GlobalTemplateHandlebars\Contract.hbs">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Documents\GlobalTemplateHandlebars\ClassicContract.hbs">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas/Payments/StripePayment/PaymentLink.hbs">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="NLog.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="packages.config" />
    <None Include="Properties\PublishProfiles\Package.pubxml" />
    <None Include="Properties\PublishProfiles\RoofSnapAPIDev.pubxml" />
    <Content Include="Static\warning.png" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Service References\Application Insights\ConnectedService.json" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Web References\com.salesforce.my.roofsnap\ChangeOwnPasswordResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DeleteByExampleResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DeleteResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeAppMenuItem.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeApprovalLayout.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeAvailableQuickActionResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeCompactLayout.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeCompactLayoutsResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeDataCategoryGroupResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeDataCategoryGroupStructureResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeDataCategoryMappingResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeGlobalResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeGlobalTheme.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeLayoutResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeNounResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribePathAssistant.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeQuickActionResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeSearchableEntityResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeSearchLayoutResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeSearchScopeOrderResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeSObjectResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeSoftphoneLayoutResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeSoqlListView.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeTab.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeTabSetResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeThemeItem.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\DescribeVisualForceResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\EmptyRecycleBinResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\ExecuteListViewResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\FindDuplicatesResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\GetDeletedResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\GetServerTimestampResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\GetUpdatedResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\GetUserInfoResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\InvalidateSessionsResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\KnowledgeSettings.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\LeadConvertResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\LoginResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\MergeResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\PerformQuickActionResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\ProcessResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\QueryResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\QuickActionTemplateResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\RenderEmailTemplateResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\RenderStoredEmailTemplateResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\ResetPasswordResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\SaveResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\SearchResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\SendEmailResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\SetPasswordResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\sObject.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\UndeleteResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\com.salesforce.my.roofsnap\UpsertResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Static\normalize.min.css.map" />
    <Content Include="Static\signature_pad.umd.min.js.map" />
    <None Include="Web References\com.salesforce.my.roofsnap\wsdl.wsdl" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Common\Documents\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RoofSnap.Core.Data\RoofSnap.Core.Data.csproj">
      <Project>{2DF72BA5-2DF3-4284-932E-8C492813F0D2}</Project>
      <Name>RoofSnap.Core.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\RoofSnap.Core.Models\RoofSnap.Core.Models.csproj">
      <Project>{a018abb8-f981-4757-9af0-08d9e01cc1b2}</Project>
      <Name>RoofSnap.Core.Models</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferenceUrl Include="https://roofsnap.my.salesforce.com/soap/wsdl.jsp%3ftype=%2a&amp;ver_help_scout=1.23&amp;ver_mkto_si=1.4371&amp;ver_rcsfl=6.6&amp;ver_relateiq=2.0&amp;ver_zdsf=1.0">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\com.salesforce.my.roofsnap\</RelPath>
      <UpdateFromURL>https://roofsnap.my.salesforce.com/soap/wsdl.jsp%3ftype=%2a&amp;ver_help_scout=1.23&amp;ver_mkto_si=1.4371&amp;ver_rcsfl=6.6&amp;ver_relateiq=2.0&amp;ver_zdsf=1.0</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>RoofSnap_WebAPI_com_salesforce_my_roofsnap_SforceService</CachedSettingsPropName>
    </WebReferenceUrl>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>0</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:58604/</IISUrl>
          <OverrideIISAppRootUrl>True</OverrideIISAppRootUrl>
          <IISAppRootUrl>http://localhost:58604/swagger</IISAppRootUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="UpdateOriginalItemSpecs" AfterTargets="ResolveAssemblyReferences">
    <ItemGroup>
      <ReferencePath>
        <OriginalItemSpec>%(ReferencePath.FileName)</OriginalItemSpec>
      </ReferencePath>
    </ItemGroup>
  </Target>
  <Import Project="..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets" Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.10.0\build\Microsoft.ApplicationInsights.DependencyCollector.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.10.0\build\Microsoft.ApplicationInsights.DependencyCollector.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.10.0\build\Microsoft.ApplicationInsights.PerfCounterCollector.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.10.0\build\Microsoft.ApplicationInsights.PerfCounterCollector.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.10.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.10.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.WindowsServer.2.10.0\build\Microsoft.ApplicationInsights.WindowsServer.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.WindowsServer.2.10.0\build\Microsoft.ApplicationInsights.WindowsServer.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.Web.2.10.0\build\Microsoft.ApplicationInsights.Web.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.Web.2.10.0\build\Microsoft.ApplicationInsights.Web.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.3.3.1\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.3.3.1\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite.2.2.1\build\netstandard2.0\Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite.2.2.1\build\netstandard2.0\Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite.targets'))" />
  </Target>
  <Import Project="..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.10.0\build\Microsoft.ApplicationInsights.DependencyCollector.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.10.0\build\Microsoft.ApplicationInsights.DependencyCollector.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.10.0\build\Microsoft.ApplicationInsights.PerfCounterCollector.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.10.0\build\Microsoft.ApplicationInsights.PerfCounterCollector.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.10.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.10.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.WindowsServer.2.10.0\build\Microsoft.ApplicationInsights.WindowsServer.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.WindowsServer.2.10.0\build\Microsoft.ApplicationInsights.WindowsServer.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.Web.2.10.0\build\Microsoft.ApplicationInsights.Web.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.Web.2.10.0\build\Microsoft.ApplicationInsights.Web.targets')" />
  <Import Project="..\packages\Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite.2.2.1\build\netstandard2.0\Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite.targets" Condition="Exists('..\packages\Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite.2.2.1\build\netstandard2.0\Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>