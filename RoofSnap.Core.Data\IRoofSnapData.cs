﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Internal;
using RoofSnap.Core.Models;

namespace RoofSnap.Core.Data
{
    public interface IRoofSnapData : IInfrastructure<IServiceProvider>, IDbContextDependencies, IDbSetCache, IDbQueryCache, IDbContextPoolable, IDisposable
    {
        DbSet<AppVersionStatusModel> AppVersionStatuses { get; set; }
        DbSet<UserProfileModel> UserProfiles { get; set; }
        DbSet<OrganizationModel> Organizations { get; set; }
        DbSet<SketchOrderModel> SketchOrders { get; set; }
        DbSet<ProjectDrawingModel> ProjectDrawings { get; set; }
        DbSet<ProjectModel> Projects { get; set; }
        DbSet<ProjectMeasurementsModel> ProjectMeasurements { get; set; }
        DbSet<ProjectImageModel> ProjectImages { get; set; }
        DbSet<ProjectDocumentModel> ProjectDocuments { get; set; }
        DbSet<OfficeModel> Offices { get; set; }
        DbSet<UserOfficeModel> UserOffices { get; set; }
        DbSet<RoleModel> Roles { get; set; }
        DbSet<UserRoleModel> UserRoles { get; set; }
        DbSet<EstimateOptionModel> EstimateOptions { get; set; }
            
        DatabaseFacade Database { get; }
        DbSet<SharedProjectModel> SharedProjects { get; set; }
        DbSet<MeasurementOrderModel> MeasurementOrders { get; set; }
        DbSet<OrganizationCreditModel> OrganizationCredits { get; set; }
        DbSet<OrganizationCreditTransactionModel> OrganizationCreditTransactions { get; set; }
        DbSet<OrganizationSettingsModel> OrganizationSettings { get; set; }
        DbSet<CreditRateBySubscriptionModel> CreditRatesBySubscription { get; set; }
        DbSet<SubscriptionTypeModel> SubscriptionTypes { get; set; }
        DbSet<OrganizationOverrideCreditRateModel> OrganizationOverrideCreditRates { get; set; }
        DbSet<OrganizationOverrideSketchCostTiersModel> OrganizationOverrideSketchCostTiers { get; set; }
        DbSet<SketchOrderCostsModel> SketchOrderCosts { get; set; }
        DbSet<SketchOrderTierModel> SketchOrderTiers { get; set; }
        DbSet<SketchOrderTiersByFacetModel> SketchOrderTiersByFacet { get; set; }
        DbSet<SketchOrderTiersBySquareModel> SketchOrderTiersBySquare { get; set; }
        DbSet<SketchTechPaymentAmountsModel> SketchTechPaymentAmounts { get; set; }
        DbSet<WebHookSubscriptionModel> WebHookSubscriptions { get; set; }
        DbSet<OfficeDefaultEdgeSubTypeModel> OfficeDefaultEdgeSubTypes { get; set; }
        DbSet<SketchOrderReportingOptionModel> SketchOrderReportingOptions { get; set; }
        DbSet<BraintreeSubscriptionIdModel> BraintreeSubscriptionIds { get; set; }
        DbQuery<ProjectEngagementModel> ProjectEngagements { get; set; }
        DbQuery<NearmapProjectEngagementModel> NearmapProjectEngagements { get; set; }
        DbQuery<ProjectMeasurementsAtPitchValueEngagementsModel> ProjectMeasurementsAtPitchValueEngagements { get; set; }
        DbSet<NotificationTemplateModel> NotificationTemplates { get; set; }
        DbSet<NotificationCustomDisplayTemplateModel> NotificationCustomDisplayTemplates { get; set; }
        DbSet<NotificationModel> Notifications { get; set; }
        DbSet<NotificationAcknowledgementModel> NotificationAcknowledgements { get; set; }
        DbSet<SketchOrderDeliveryNotificationModel> SketchOrderDeliveryNotifications { get; set; }
        DbSet<OrganizationOverrideNotificationPriorityLevelModel> OrganizationOverrideNotificationPriorityLevels { get; set; }
        DbSet<OrganizationFreeSketchOrderRateModel> OrganizationFreeSketchOrderRates { get; set; }
        DbSet<OrganizationFreeSketchOrderBalanceModel> OrganizationFreeSketchOrderBalances { get; set; }
        DbSet<DocumentV2TemplateModel> DocumentTemplates { get; set; }
        DbSet<DocumentV2TemplateCategoryModel> DocumentTemplateCategories { get; set; }
        DbSet<DocumentV2Model> Documents { get; set; }
        DbSet<DocumentV2RenderingModel> DocumentRenderings { get; set; }
        DbSet<DocumentV2ExternalImplementerValueModel> DocumentExternalImplementerValues { get; set; }
        DbSet<DocumentV2TemplatePermissionsModel> DocumentTemplatePermissions { get; set; }
        DbSet<EstimateRuleModel> EstimateRules { get; set; }
        DbSet<BraintreeSubscriptionRecordModel> BraintreeSubscriptionRecords { get; set; }
        DbSet<BraintreeTransactionRecordModel> BraintreeTransactionRecords { get; set; }
        DbSet<ReceiptLogModel> ReceiptLogs { get; set; }
        DbSet<NearmapOrderModel> NearmapOrders { get; set; }
        DbSet<NearmapOrderBillingModel> NearmapOrderBillings { get; set; }
        DbSet<BlueRoofOrderModel> BlueRoofOrders { get; set; }
        DbSet<SketchOrderGroupModel> SketchOrderGroups { get; set; }
        DbQuery<OfficeContractTermsModel> OfficeContractTerms { get; set; }
        DbSet<OfficeV2ContractTermsModel> OfficeV2ContractTerms { get; set; }
        DbSet<EstimateTemplateModel> EstimateTemplates { get; set; }
        DbSet<EstimateTemplateItemModel> EstimateTemplateItems { get; set; }

        DbSet<OfficePricedChargeableItemModel> OfficePricedChargeableItems { get; set; }
        DbSet<RushOrderSettingsModel> RushOrderSettings { get; set; }
        
        DbSet<StripePaymentRequest> StripePaymentRequests { get; set; }
        DbSet<ProjectSnapEstimateModel> ProjectSnapEstimates { get; set; }
        DbSet<ProjectSnapEstimatesLineItemsMasterModel> ProjectSnapEstimatesLineItemsMaster { get; set; }

        /// <inheritdoc cref="DbContext.Entry"/>
        EntityEntry<TEntity> Entry<TEntity>(TEntity entity) where TEntity : class;

        /// <inheritdoc cref="DbContext.SaveChangesAsync(bool, CancellationToken)"/>
        Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default);

        /// <inheritdoc cref="DbContext.SaveChangesAsync(CancellationToken)"/>
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

        /// <inheritdoc cref="RelationalDatabaseFacadeExtensions.ExecuteSqlCommandAsync(DatabaseFacade, RawSqlString, object[])"/>
        Task<int> ExecuteSqlCommandAsync(RawSqlString sql, params object[] parameters);

        /// <inheritdoc cref="DbContext.Set{TEntity}"/>
        DbSet<TEntity> Set<TEntity>() where TEntity : class;

        /// <inheritdoc cref="DbContext.SaveChanges()"/>
        int SaveChanges();

        void SetVersion<TEntity>(TEntity entity, byte[] version) where TEntity : class;
    }
}