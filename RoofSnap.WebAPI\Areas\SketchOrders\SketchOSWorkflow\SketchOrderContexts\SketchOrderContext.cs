﻿using RoofSnap.Core.Models;
using RoofSnap.WebAPI.Areas.Billing;
using RoofSnap.WebAPI.Areas.SketchOrders.Data;
using RoofSnap.WebAPI.Areas.SketchOrders.SketchOSWorkflow.SketchOrderStates;
using RoofSnap.WebAPI.Areas.SketchOrders.SketchServiceBilling;
using RoofSnap.WebAPI.Areas.SketchOrders.SketchServiceBilling.Mailer.Refund;
using RoofSnap.WebAPI.Common.Billing;
using RoofSnap.WebAPI.Common.Data;
using RoofSnap.WebAPI.Common.Events;
using RoofSnap.WebAPI.Common.Wrappers;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace RoofSnap.WebAPI.Areas.SketchOrders.SketchOSWorkflow.SketchOrderContexts
{
    public interface ISketchOrderContext
    {
        Task UpdateSketchOrderAsync(byte[] version, SketchOrderContextAccessMode accessMode);

        /// <summary>
        ///     Will return the same DbSketchOrder passed in through the context's constructor, with 'extra' properties filled in
        ///     according to the SketchOrder's state.
        /// </summary>
        DbSketchOrder GetSketchOrder(SketchOrderContextAccessMode accessMode = SketchOrderContextAccessMode.None);

        Task<UpdatePaymentMethodResult> UpdatePaymentMethodAsync(string paymentMethodNonce);

        /// <summary>
        ///     Will bypass the status transition checks implemented by UpdateSketchOrderAsync.
        /// </summary>
        Task UpdateSketchOrderStatusAsync(SketchOrderStatusModel SketchOrderStatusModel);

        void AddBillingSettlementCallbacks(IBillingSettlementCallbacks billingSettlementCallbacks);
        void AddRefundCallbacks(IRefundCallbacks refundCallbacks);

        Task RefundSketchOrderAsync(SketchOrderRefund refund);
        Task BeginDistributedLockAsync();

        Task DeliverProject();
    }

    public class SketchOrderContext : ISketchOrderContext
    {
        private readonly IBillingFailedSketchOrderEmailer _billingFailedSketchOrderEmailer;
        private readonly IRoofSnapRepository _roofSnapRepository;
        private readonly DbSketchOrder _sketchOrder;
        private readonly ISketchOrderRefundMailer _sketchOrderRefundMailer;
        private readonly IRoofSnapLiveDbContext _dbContext;
        private readonly ISketchOrderDeliverer _sketchOrderDeliverer;
        private readonly IRoofSnapApiEventPublisher _roofSnapApiEventPublisher;
        private readonly IDateTimeOffsetWrapper _dateTimeOffsetWrapper;
        private readonly ISketchOrderStateFactory _sketchOrderStateFactory;

        private readonly Queue<SketchOrderStatusModel> _statusTransitionQueue = new Queue<SketchOrderStatusModel>();
        private SketchOrderStatusModel _currentStatus;

        private ISketchOrderState _sketchOrderState;

        public SketchOrderContext(DbSketchOrder sketchOrder,
            SketchOrderStatusModel currentStatus,
            ISketchOrderStateFactory sketchOrderStateFactory,
            IBillingFailedSketchOrderEmailer billingFailedSketchOrderEmailer,
            IRoofSnapRepository roofSnapRepository,
            ISketchOrderRefundMailer sketchOrderRefundMailer, IRoofSnapLiveDbContext dbContext,
            ISketchOrderDeliverer sketchOrderDeliverer, IRoofSnapApiEventPublisher roofSnapApiEventPublisher,
            IDateTimeOffsetWrapper dateTimeOffsetWrapper)
        {
            _sketchOrder = sketchOrder;
            _currentStatus = currentStatus;
            _sketchOrderStateFactory = sketchOrderStateFactory;
            _billingFailedSketchOrderEmailer = billingFailedSketchOrderEmailer;
            _roofSnapRepository = roofSnapRepository;
            _sketchOrderRefundMailer = sketchOrderRefundMailer;
            _dbContext = dbContext;
            _sketchOrderDeliverer = sketchOrderDeliverer;
            _roofSnapApiEventPublisher = roofSnapApiEventPublisher;
            _dateTimeOffsetWrapper = dateTimeOffsetWrapper;

            _sketchOrderState = sketchOrderStateFactory.Create(currentStatus);
            WireEventHandlers(_sketchOrderState);
        }

        public async Task UpdateSketchOrderAsync(byte[] version, SketchOrderContextAccessMode accessMode)
        {
            var originalStatus = _currentStatus;
            var statusHasChanged = originalStatus != _sketchOrder.SketchOrderStatus;

            await _sketchOrderState.UpdateSketchOrderAsync(this, version, accessMode);

            if (statusHasChanged)
            {
                _statusTransitionQueue.Enqueue(_sketchOrder.SketchOrderStatus);

                var previousStatus = originalStatus;

                // First make sure the next status transition is allowed for the given access mode.
                var allowedTransitions = _sketchOrderState.GetAllowedTransitions(accessMode);
                if (!allowedTransitions.Contains(_statusTransitionQueue.Peek()))
                    throw new InvalidSketchOrderStatusTransitionException(accessMode, previousStatus,
                                                                          _statusTransitionQueue.Peek());

                await ProcessTransitionQueueAsync(previousStatus);
            }
        }

        public async Task RefundSketchOrderAsync(SketchOrderRefund refund)
        {
            if (_sketchOrderState is IRefundSketchOrder refundableOrder)
            {
                var sketchOrderCostBeforeRefund = _sketchOrder.Cost.GetValueOrDefault();
                // cannot refund the sketch order beyond a zero value, this gives us a bit of padding for conversion errors
                if (sketchOrderCostBeforeRefund - Convert.ToDouble(refund.RefundAmount) < -.001)
                    throw new CannotRefundMoreThanTheWorthOfTheSketchOrderException(_sketchOrder.Id,
                        refund.RefundAmount);

                await refundableOrder.RefundAsync(this, refund);
                _sketchOrderRefundMailer.SendEmailToRequester(_sketchOrder, refund, sketchOrderCostBeforeRefund);
                await ProcessTransitionQueueAsync(_sketchOrder.SketchOrderStatus);
            }
            else
            {
                throw new SketchOrderIsNotInARefundableStateException(_sketchOrder.Id);
            }
        }

        public async Task BeginDistributedLockAsync()
        {
            await _roofSnapRepository.BeginDistributedLockAsync(
                SketchOrderBillingConstants.DistributedLockKeyPrefix + _sketchOrder.Organizationid);
        }

        /// <inheritdoc cref="ISketchOrderContext.GetSketchOrder" />
        public DbSketchOrder GetSketchOrder(SketchOrderContextAccessMode accessMode = SketchOrderContextAccessMode.None)
        {
            // Fill in AllowedStatusTransitions
            _sketchOrder.AllowedTransitions = _sketchOrderState.GetAllowedTransitions(accessMode);

            // Fill in EditableProperties
            _sketchOrder.EditableProperties = _sketchOrderState.GetEditablePropertyNames(accessMode);

            return _sketchOrder;
        }

        public async Task<UpdatePaymentMethodResult> UpdatePaymentMethodAsync(string paymentMethodNonce)
        {
            return await _sketchOrderState.UpdatePaymentMethodAsync(this, paymentMethodNonce);
        }

        public async Task UpdateSketchOrderStatusAsync(SketchOrderStatusModel SketchOrderStatusModel)
        {
            _statusTransitionQueue.Enqueue(SketchOrderStatusModel);
            await ProcessTransitionQueueAsync(_currentStatus);
        }

        public void AddBillingSettlementCallbacks(IBillingSettlementCallbacks billingSettlementCallbacks)
        {
            _sketchOrderState.AddBillingSettlementCallbacks(this, billingSettlementCallbacks);
        }

        public void AddRefundCallbacks(IRefundCallbacks refundCallbacks)
        {
            _sketchOrderState.AddRefundCallbacks(this, refundCallbacks);
        }

        public async Task DeliverProject()
        {
            var sketchOrder = GetSketchOrder();
            string projectId = sketchOrder.ProjectId; //get the old projectId
                                                      // 'Cost' will be billed to the customer, which could be different than the cost of the sketch order for promotions/discounts/whatever
                                                      // 'Initialcost' is a record of what the Cost would have been without any discounts applied
            sketchOrder.Initialcost = sketchOrder.Cost;
            sketchOrder.Completeddate = _dateTimeOffsetWrapper.Now;

            // If it has already been cloned for delivery, we're counting it as delivered
            if (!sketchOrder.ProjectClonedForDelivery)
            {
                // If the project has measurements, save off the square count.
                var projectMeasurements =
                    await _dbContext.ProjectMeasurements.FirstOrDefaultAsync(project =>
                        project.Id == sketchOrder.ProjectId);
                if (projectMeasurements?.ActualSquares != null)
                    sketchOrder.ActualSquaresOnComplete = projectMeasurements.ActualSquares;

                // Move the project to the 'orders' account for archiving
                sketchOrder.Project.ProjectOwner = "<EMAIL>";

                await _sketchOrderDeliverer.DeliverAsync(sketchOrder);
            }

            if (sketchOrder.RevisionNotes.Any()) await _sketchOrderDeliverer.DeliverAsync(sketchOrder, false);

            await _sketchOrderDeliverer.CloneInProgressDocumentsAsync(projectId, sketchOrder.ProjectId);

            _roofSnapApiEventPublisher.PublishSketchOrderCompletedEvent(sketchOrder);
        }

        private void OrderBilledEventHandler(object sender, SketchOrderBilledEventArgs eventArgs)
        {
            if (!eventArgs.BillingSucceeded && _sketchOrder.OrderPaymentType == SketchOrderPaymentType.User)
                _billingFailedSketchOrderEmailer.SendEmailToRequester(_sketchOrder);

            _statusTransitionQueue.Enqueue(eventArgs.BillingSucceeded
                ? SketchOrderStatusModel.Billed
                : SketchOrderStatusModel.BillingFailed);
        }

        private void OrderRefundedToZeroEventHandler(object sender, EventArgs args = null)
        {
            _statusTransitionQueue.Enqueue(SketchOrderStatusModel.Complete);
        }

        private void GroupOrderProcessedEventHandler(object sender, EventArgs args = null)
        {
            _statusTransitionQueue.Enqueue(SketchOrderStatusModel.PendingGroupBilling);
        }

        private async Task ProcessTransitionQueueAsync(SketchOrderStatusModel previousStatus)
        {
            while (_statusTransitionQueue.Any())
            {
                var nextStatus = _statusTransitionQueue.Dequeue();

                // Inform the previous state that it is being exited, giving it a chance to stop the transition.
                var continueTransition = await _sketchOrderState.OnExitingStateAsync(this, nextStatus);

                // If OnExitingStateAsync() came back false, stop the status transition.
                if (!continueTransition)
                {
                    _sketchOrder.SketchOrderStatus = previousStatus;
                    continue;
                }

                var nextSketchOrderState = _sketchOrderStateFactory.Create(nextStatus);
                WireEventHandlers(nextSketchOrderState);
                continueTransition = await nextSketchOrderState.OnEnteringStateAsync(this, previousStatus);

                if (!continueTransition)
                {
                    _sketchOrder.SketchOrderStatus = previousStatus;
                    continue;
                }


                // Change the sketch order status
                _sketchOrder.SketchOrderStatus = nextStatus;

                // Inform the previous state it has been exited.
                await _sketchOrderState.OnExitedStateAsync(this, nextStatus);
                UnwireEventHandlers(_sketchOrderState);

                // Create & assign the next SketchOrderState. Events are rewired in the property setter.
                _sketchOrderState = nextSketchOrderState;

                // Inform the new state it has been entered.
                await _sketchOrderState.OnEnteredStateAsync(this, previousStatus);

                _currentStatus = nextStatus;
            }
        }

        private void WireEventHandlers(ISketchOrderState sketchOrderState)
        {
            sketchOrderState.OrderBilled += OrderBilledEventHandler;
            sketchOrderState.OrderRefundedToZero += OrderRefundedToZeroEventHandler;
            sketchOrderState.OnGroupOrderProcessed += GroupOrderProcessedEventHandler;
        }

        private void UnwireEventHandlers(ISketchOrderState sketchOrderState)
        {
            sketchOrderState.OrderBilled -= OrderBilledEventHandler;
            sketchOrderState.OrderRefundedToZero -= OrderRefundedToZeroEventHandler;
            sketchOrderState.OnGroupOrderProcessed -= GroupOrderProcessedEventHandler;
        }
    }

    [Serializable]
    public class CannotRefundMoreThanTheWorthOfTheSketchOrderException : Exception
    {
        public CannotRefundMoreThanTheWorthOfTheSketchOrderException(long sketchOrderId, decimal refundAmount)
            : base($"{sketchOrderId} currently has a greater cost than {refundAmount}")
        {
        }

        protected CannotRefundMoreThanTheWorthOfTheSketchOrderException(SerializationInfo serializationInfo,
            StreamingContext context) : base(serializationInfo, context)
        {
        }
    }
}