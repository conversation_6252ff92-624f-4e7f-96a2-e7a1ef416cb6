﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RoofSnap.Core.Data;
using RoofSnap.Core.Models;
using RoofSnap.WebAPI.Areas.Projects.ProjectDrawings;
using RoofSnap.WebAPI.Common;
using RoofSnap.WebAPI.Common.BlobStorage;
using RoofSnap.WebAPI.Common.Config;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RoofSnap.WebAPI.Areas.Projects
{
    public interface IProjectCloner
    {
        /// <summary>
        /// Clone project to same owner/office/org
        /// this assumes the calling function will flush the RoofSnapData (EF Core db context)
        /// </summary>
        /// <exception cref="ArgumentException"></exception>
        Task CloneAsync(string projectIdOrShortCode);

        /// <summary>
        /// Clone project to a new user, and their office and org
        /// this assumes the calling function will flush the RoofSnapData (EF Core db context)
        /// </summary>
        /// <returns>ProjectId of the cloned project.</returns>
        /// <exception cref="ArgumentException"></exception>
        Task<string> CloneAsync(string projectIdOrShortCode, int userIdToCopyTo, long? officeId = null);

        /// <summary>
        /// Clone project to a new user in their office and org, with given new project name
        /// this assumes the calling function will flush the RoofSnapData (EF Core db context)
        /// </summary>
        /// <returns>ProjectId of the cloned project.</returns>
        /// <exception cref="ArgumentException"></exception>
        Task<string> CloneAsync(string projectIdOrShortCode, int userIdToCopyTo, string newProjectName);

        Task CloneSampleProjects(int userIdToCopyTo);
    }

    public class ProjectCloner : IProjectCloner
    {
        private readonly IRoofSnapData _roofSnapData;
        private readonly IMapper _mapper;
        private readonly IProjectDrawingPinExtractor _projectDrawingPinExtractor;
        private readonly IBlobStorageFactory _blobStorageFactory;
        private readonly IConfig _config;

        public ProjectCloner(IMapper mapper,
            IProjectDrawingPinExtractor projectDrawingPinExtractor,
            IBlobStorageFactory blobStorageFactory,
            IConfig config,
            IRoofSnapData roofSnapData)
        {
            _mapper = mapper;
            _projectDrawingPinExtractor = projectDrawingPinExtractor;
            _blobStorageFactory = blobStorageFactory;
            _config = config;
            _roofSnapData = roofSnapData;
        }

        /// <inheritdoc />
        public Task CloneAsync(string projectIdOrShortCode)
        {
            if (string.IsNullOrEmpty(projectIdOrShortCode))
                throw new ArgumentException("Argument cannot be empty.", nameof(projectIdOrShortCode));

            return CloneAndSaveImplementationAsync(projectIdOrShortCode);
        }

        /// <inheritdoc />
        public async Task<string> CloneAsync(string projectIdOrShortCode, int userIdToCopyTo, long? officeId = null)
        {
            if (string.IsNullOrEmpty(projectIdOrShortCode))
                throw new ArgumentException("Argument cannot be empty.", nameof(projectIdOrShortCode));

            var projectModel = await CloneAndSaveImplementationAsync(projectIdOrShortCode, userIdToCopyTo, false, officeId);
            return projectModel.Id;
        }

        /// <inheritdoc />
        public async Task<string> CloneAsync(string projectIdOrShortCode, int userIdToCopyTo, string newProjectName)
        {
            if (string.IsNullOrEmpty(projectIdOrShortCode))
                throw new ArgumentException("Argument cannot be empty.", nameof(projectIdOrShortCode));

            var projectModel = await CloneAndSaveImplementationAsync(projectIdOrShortCode, userIdToCopyTo);
            projectModel.ProjectName = newProjectName;
            return projectModel.Id;
        }

        public async Task CloneSampleProjects(int userIdToCopyTo)
        {
            if (await UserAlreadyHasSampleProjects(userIdToCopyTo)) return;

            var sampleOfficeToCopyProjectsFrom = _config.SampleOfficeToCopyProjectsFrom;
            if (sampleOfficeToCopyProjectsFrom == 0)
                return;
            
            var projectLimit = _config.SampleOfficeToCopyProjectsFromProjectLimit;
            //grab sample projects by office and copy to user
            var sampleProjectIds = await _roofSnapData.Projects
                                                      .Where(p => p.OfficeId == sampleOfficeToCopyProjectsFrom)
                                                      .Select(p => p.Id)
                                                      .Take(projectLimit)
                                                      .ToArrayAsync();

            await CloneAndSaveImplementationsAsync(userIdToCopyTo, sampleProjectIds);
        }

        private async Task<bool> UserAlreadyHasSampleProjects(int userId)
        {
            var userProfile = await _roofSnapData.UserProfiles
                                                 .Include(up => up.OwnedProjects)
                                                 .FirstAsync(up => up.UserId == userId);

            return userProfile.OwnedProjects.Any(p => p.IsSampleProject);
        }
        
        private async Task CloneAndSaveImplementationsAsync(int? userIdToCopyTo, IList<string> sampleProjectIds)
        {
            foreach (var id in sampleProjectIds)
            {
                await CloneAndSaveImplementationAsync(id, userIdToCopyTo, true);
            }
            
            await _roofSnapData.SaveChangesAsync();
        }

        private async Task<ProjectModel> CloneAndSaveImplementationAsync(string projectIdOrShortCode,
                                                                         int? userIdToCopyTo = null,
                                                                         bool markAsSampleProject = false,
                                                                         long? officeId = null)
        {
            var clonedProject = await CloneImplementationAsync(projectIdOrShortCode, userIdToCopyTo, markAsSampleProject, officeId);
            await _roofSnapData.SaveChangesAsync();
            return clonedProject;
        }

        private async Task<ProjectModel> CloneImplementationAsync(string projectIdOrShortCode,
                                                                  int? userIdToCopyTo = null, 
                                                                  bool markAsSampleProject = false, 
                                                                  long? officeId = null)
        {
            var originalProject = await _roofSnapData.Projects
                                                     .Include(p => p.ProjectMeasurements)
                                                     .Include(p => p.ProjectImages)
                                                     .Include(p => p.EstimateOptions)
                                                     .FirstOrDefaultAsync(p => p.Id == projectIdOrShortCode || p.ShortCode == projectIdOrShortCode);
         
            if (originalProject == null)
                throw new EntityNotFoundException<ProjectModel>(projectIdOrShortCode);
   
            string projectOwner;
            long organizationId;

            if (!userIdToCopyTo.HasValue)
            {
                projectOwner = originalProject.ProjectOwner;
                organizationId = originalProject.OrganizationId.Value;
                if (!officeId.HasValue)
                    officeId = originalProject.OfficeId.Value;
            }
            else
            {
                UserProfileModel newUserProfile = await _roofSnapData.UserProfiles
                    .Include(u => u.UserOffices)
                    .ThenInclude(o => o.Office)
                    .FirstOrDefaultAsync(u => u.UserId == userIdToCopyTo);

                if (newUserProfile == null)
                    throw new EntityNotFoundException<UserProfileModel>(userIdToCopyTo.Value);
                OfficeModel office;
                if (!officeId.HasValue)
                {
                    office = newUserProfile.UserOffices
                                 .FirstOrDefault(userOffice => 
                                     userOffice.OfficeId == originalProject.OfficeId)?.Office
                             ??
                             newUserProfile.UserOffices
                                 .OrderByDescending(userOffice => userOffice.OfficeId)
                                 .First().Office;
                }
                else
                {
                    office = await _roofSnapData.Offices.FirstOrDefaultAsync(o => o.Id == officeId);
                }

                officeId = office.Id;
                organizationId = office.OrganizationId;
                projectOwner = newUserProfile.UserName;
            }
            
            //clone project
            var clonedProject = _mapper.Map<ProjectModel>(originalProject);
            clonedProject.OrganizationId = organizationId;
            clonedProject.OfficeId = officeId;
            clonedProject.ProjectOwner = projectOwner;
            clonedProject.IsSampleProject = markAsSampleProject;
            await _roofSnapData.Projects.AddAsync(clonedProject);

            //clone Estimate
            if (originalProject.EstimateOptions?.Any() == true && originalProject.ProjectOwner == (_config.SketchOrderProjectOwner))
            {
                var clonedEstimates = originalProject.EstimateOptions
                    .Select(originalEstimate =>
                    {
                        var cloned = _mapper.Map<EstimateOptionModel>(originalEstimate);
                        cloned.ProjectId = clonedProject.Id;
                        return cloned;
                    });

                _roofSnapData.EstimateOptions.UpdateRange(clonedEstimates);
                await _roofSnapData.SaveChangesAsync();
            }

            //clone measurement
            if (originalProject.ProjectMeasurements != null)
            {
                var clonedProjectMeasurements = _mapper.Map<ProjectMeasurementsModel>(originalProject.ProjectMeasurements);
                clonedProjectMeasurements.Id = clonedProject.Id;

                await _roofSnapData.ProjectMeasurements.AddAsync(clonedProjectMeasurements);
            }

            //project drawing and image in blob storage
            if (!originalProject.HasDrawing.HasValue || !originalProject.HasDrawing.Value) 
                return clonedProject;
            
            //check imageurl
            if (!string.IsNullOrEmpty(clonedProject.RoofSnapImageUrl))
            {
                clonedProject.RoofSnapImageUrl = clonedProject.RoofSnapImageUrl
                                                              .Replace(originalProject.Id.ToLower(),
                                                                       clonedProject.Id.ToLower());
            }

            //get drawing and clone if the drawing exists
            var projectDrawing = await _roofSnapData.ProjectDrawings
                                                    .FirstOrDefaultAsync(pd => pd.ProjectId == originalProject.Id);

            if (projectDrawing != null)
            {
                var clonedProjectDrawing = _mapper.Map<ProjectDrawingModel>(projectDrawing);
                clonedProjectDrawing.ProjectId = clonedProject.Id;
                    
                if (clonedProject.OfficeId.HasValue && originalProject.OfficeId.HasValue && 
                    clonedProject.OfficeId.Value != originalProject.OfficeId.Value)
                {
                    _projectDrawingPinExtractor.ConvertPins(clonedProjectDrawing, originalProject.OfficeId.Value, clonedProject.OfficeId.Value);
                }

                await _roofSnapData.ProjectDrawings.AddAsync(clonedProjectDrawing);
            }

            //clone image files from blob storage
            if (originalProject.ProjectImages == null) 
                return clonedProject;
                
            foreach (var image in originalProject.ProjectImages)
            {
                var clonedImage = _mapper.Map<ProjectImageModel>(image);
                clonedImage.ProjectId = clonedProject.Id;
                clonedImage.ImageUrl = clonedImage.ImageUrl
                                                  .Replace(originalProject.Id.ToLower(), 
                                                           clonedProject.Id.ToLower());
                clonedImage.OrganizationId = organizationId;
                
                await _roofSnapData.ProjectImages.AddAsync(clonedImage);
            }
                                     
            var originalContainerTask = _blobStorageFactory.CreateProjectContainerForReadAsync(originalProject.Id);
            var clonedContainerTask = _blobStorageFactory.CreateProjectContainerForWriteAsync(clonedProject.Id);
                    
            await Task.WhenAll(originalContainerTask, clonedContainerTask);
                    
            _ = clonedContainerTask.Result.CopyDirectoryFromAsync(originalContainerTask.Result);

            return clonedProject;
        }
    }
}