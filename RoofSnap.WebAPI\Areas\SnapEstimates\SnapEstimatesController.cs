﻿using RoofSnap.Core.Models;
using RoofSnap.WebAPI.Areas.Projects;
using RoofSnap.WebAPI.Common.Data;
using RoofSnap.WebAPI.Common.Logging;
using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;
using System.Web.Http.Results;

namespace RoofSnap.WebAPI.Areas.SnapEstimates
{
    [RoutePrefix("v1/snapestimates")]
    [Authorize]
    public class SnapEstimatesController : ApiController
    {
        private readonly IErrorLogger _errorLogger;
        private readonly ISnapEstimateService _snapEstimateService;
        private readonly IProjectService _projectService;
        private readonly IUnitOfWork _unitOfWork;

        public SnapEstimatesController(
            IErrorLogger errorLogger,
            ISnapEstimateService snapEstimateService,
            IProjectService projectService,
            IUnitOfWork unitOfWork)
        {
            _errorLogger = errorLogger;
            _snapEstimateService = snapEstimateService;
            _projectService = projectService;
            _unitOfWork = unitOfWork;
        }

        [HttpPost]
        [Route("")]
        public async Task<IHttpActionResult> CreateSnapEstimate(CreateSnapEstimateRequestDto dto)
        {
            var validationError = ValidateCreateSnapEstimateRequest(dto);
            if (validationError != null)
            {
                return BadRequest(validationError);
            }

            var project = await _projectService.Get(dto.ProjectId).GetSingleResultAsync();
            if (project == null)
            {
                return NotFound();
            }

            if (!await EnsureProjectAddressAsync(project, dto))
            {
                return BadRequest($"No address provided for project {dto.ProjectId}");
            }

            try
            {
                var snapEstimate = await _snapEstimateService.CreateSnapEstimate(dto);
                var costRange = CalculateCostRange(snapEstimate);
                var response = BuildResponse(snapEstimate, costRange);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _errorLogger.LogError(ex);
                return InternalServerError();
            }
        }

        [HttpPut]
        [Route("{id}/rename")]
        public async Task<IHttpActionResult> Rename(string id, UpdateSnapEstimateDto dto)
        {
            if (string.IsNullOrEmpty(dto.Name))
            {
                return BadRequest("The Name parameter cannot be null or empty.");
            }
            try
            {
                await _unitOfWork.RunAsync(async () => await _snapEstimateService.UpdateNameAsync(id, dto.Name));
                return Ok("");
            }
            catch (Exception ex)
            {
                _errorLogger.LogError(ex);
                return InternalServerError();
            }
        }


        private async Task<bool> EnsureProjectAddressAsync(ProjectModel project, CreateSnapEstimateRequestDto dto)
        {
            if (string.IsNullOrEmpty(project.CustomerAddress))
            {
                if (!string.IsNullOrEmpty(dto.CustomerAddress))
                {
                    var addressInfo = new AddressInfo(
                        dto.CustomerAddress,
                        dto.CustomerCity,
                        dto.CustomerState,
                        dto.CustomerZip,
                        dto.MapCenterLat,
                        dto.MapCenterLon);
                    return await _projectService.SaveProjectAddressInfo(project, addressInfo);
                }
                return false;
            }
            else
            {
                dto.CustomerAddress = project.CustomerAddress;
                return true;
            }
        }

        private string CalculateCostRange(ProjectSnapEstimateModel snapEstimate)
        {
            var minCost = snapEstimate.TotalCostPerSquare * snapEstimate.RoofArea * (1 - snapEstimate.PriceRange / 200);
            var maxCost = snapEstimate.TotalCostPerSquare * snapEstimate.RoofArea * (1 + snapEstimate.PriceRange / 200);
            return $"${minCost:F2} - ${maxCost:F2}";
        }

        private object BuildResponse(ProjectSnapEstimateModel snapEstimate, string costRange)
        {
            return new
            {
                snapEstimate.Id,
                snapEstimate.Name,
                snapEstimate.ProjectId,
                snapEstimate.Description,
                snapEstimate.RoofMaterialName,
                snapEstimate.RoofArea,
                snapEstimate.MaterialCostPerSquare,
                snapEstimate.LaborCostPerSquare,
                snapEstimate.ProfitMarginAndMarkup,
                snapEstimate.TotalCostPerSquare,
                snapEstimate.PriceRange,
                snapEstimate.LineItems,
                snapEstimate.CreatedAt,
                snapEstimate.UpdatedAt,
                CostRange = costRange,
            };
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IHttpActionResult> DeleteSnapEstimate(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return BadRequest("The id parameter cannot be null or empty.");
            }

            try
            {
                var snapEstimate = await _unitOfWork.RunAsync(async () => await _snapEstimateService.DeleteSnapEstimateAsync(id));

                if (snapEstimate == null)
                {
                    return NotFound();
                }

                ResponseMessageResult result = ResponseMessage(Request.CreateResponse(HttpStatusCode.NoContent));

                return result;
            }
            catch (Exception ex)
            {
                _errorLogger.LogError(ex);
                return InternalServerError();
            }
        }

        [HttpPut]
        [Route("{id}")]
        public async Task<IHttpActionResult> UpdateSnapEstimate(string id, UpdateSnapEstimateDto dto)
        {
            if (string.IsNullOrEmpty(id))
            {
                return BadRequest("The id parameter cannot be null or empty.");
            }

            var validationError = ValidateUpdateSnapEstimateRequest(dto);
            if (validationError != null)
            {
                return BadRequest(validationError);
            }

            try
            {
                var snapEstimate = await _unitOfWork.RunAsync(async () => await _snapEstimateService.UpdateSnapEstimateAsync(id, dto));

                if (snapEstimate == null)
                {
                    return NotFound();
                }

                var response = new
                {
                    snapEstimate.Id,
                    snapEstimate.Name,
                    snapEstimate.ProjectId,
                    snapEstimate.Description,
                    snapEstimate.RoofMaterialName,
                    snapEstimate.RoofArea,
                    snapEstimate.MaterialCostPerSquare,
                    snapEstimate.LaborCostPerSquare,
                    snapEstimate.ProfitMarginAndMarkup,
                    snapEstimate.TotalCostPerSquare,
                    snapEstimate.PriceRange,
                    snapEstimate.LineItems,
                    snapEstimate.TaxPercentage,
                    snapEstimate.IsChosen,
                    snapEstimate.DiscountPercentage,
                    snapEstimate.DiscountDescription,
                    snapEstimate.CreatedAt,
                    snapEstimate.UpdatedAt
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _errorLogger.LogError(ex);
                return InternalServerError();
            }
        }

        /// <summary>
        /// Updates a single line item within a snap estimate
        /// </summary>
        /// <param name="id">The snap estimate ID</param>
        /// <param name="lineItemId">The line item ID to update</param>
        /// <param name="lineItemDto">The line item update data</param>
        /// <returns>The updated line item if successful</returns>
        /// <response code="200">Line item updated successfully</response>
        /// <response code="400">Invalid request parameters or validation failed</response>
        /// <response code="404">Snap estimate or line item not found</response>
        /// <response code="500">Internal server error</response>
        [HttpPut]
        [Route("{id}/lineitems/{lineItemId}")]
        [ResponseType(typeof(LineItemDto))]
        public async Task<IHttpActionResult> UpdateLineItem(string id, int lineItemId, LineItemDto lineItemDto)
        {
            if (string.IsNullOrEmpty(id))
            {
                return BadRequest("The id parameter cannot be null or empty.");
            }

            if (lineItemId == 0)
            {
                return BadRequest("The lineItemId parameter should be a valid number.");
            }

            var validationError = ValidateLineItemRequest(lineItemDto);
            if (validationError != null)
            {
                return BadRequest(validationError);
            }

            try
            {
                var updatedLineItem = await _unitOfWork.RunAsync(
                    async () => await _snapEstimateService.UpdateLineItemAsync(id, lineItemId, lineItemDto)
                );

                if (updatedLineItem == null)
                {
                    return NotFound();
                }

                return Ok(updatedLineItem);
            }
            catch (Exception ex)
            {
                _errorLogger.LogError(ex);
                return InternalServerError();
            }
        }


        [HttpGet]
        [Route("{id}")]
        public async Task<IHttpActionResult> GetSnapEstimate(string id)
        {
            SnapEstimateRepresentation snapEstimate = await _unitOfWork.RunAsync(async () => await _snapEstimateService.GetAsync(id));

            if (snapEstimate == null)
                return NotFound();

            return Ok(snapEstimate);
        }

        [HttpGet]
        [Route("roofmaterials")]
        public async Task<IHttpActionResult> GetUniqueRoofMaterials()
        {
            try
            {
                var roofMaterials = await _unitOfWork.RunAsync(async () => await _snapEstimateService.GetUniqueRoofMaterialsAsync());
                return Ok(roofMaterials);
            }
            catch (Exception ex)
            {
                _errorLogger.LogError(ex);
                return InternalServerError();
            }
        }

        private static string ValidateCreateSnapEstimateRequest(CreateSnapEstimateRequestDto dto)
        {
            if (dto == null)
            {
                return "Request body cannot be null.";
            }

            dto.ProjectId = dto.ProjectId?.Trim();

            return ValidateSnapEstimateDto(dto);
        }

        private static string ValidateUpdateSnapEstimateRequest(UpdateSnapEstimateDto dto)
        {
            if (dto == null)
            {
                return "Request body cannot be null.";
            }

            return string.IsNullOrEmpty(dto.LineItems)
                       ? "The LineItems field is required."
                       : ValidateSnapEstimateDto(dto);
        }

        private static string ValidateSnapEstimateDto(BaseSnapEstimateDto dto)
        {
            dto.RoofMaterialName = dto.RoofMaterialName?.Trim();
            dto.Description = dto.Description?.Trim();

            if (string.IsNullOrEmpty(dto.RoofMaterialName))
            {
                return "The RoofMaterialName field is required.";
            }

            if (dto.RoofArea <= 0)
            {
                return "The RoofArea field must be greater than 0.";
            }

            if (dto.MaterialCostPerSquare <= 0)
            {
                return "The MaterialCostPerSquare field must be greater than 0.";
            }

            if (dto.LaborCostPerSquare <= 0)
            {
                return "The LaborCostPerSquare field must be greater than 0.";
            }

            if (dto.ProfitMarginAndMarkup < 0)
            {
                return "The ProfitMarginAndMarkup field cannot be negative.";
            }

            if (dto.PriceRange < 0)
            {
                return "The PriceRange field cannot be negative.";
            }

            if (dto.TotalCostPerSquare <= 0)
            {
                return "The TotalCostPerSquare field must be greater than 0.";
            }

            return null;
        }

        private static string ValidateLineItemRequest(LineItemDto lineItemDto)
        {
            if (lineItemDto == null)
            {
                return "Request body cannot be null.";
            }

            if (string.IsNullOrEmpty(lineItemDto.Name))
            {
                return "Name cannot be null or empty.";
            }

            return null;
        }
    }
}
