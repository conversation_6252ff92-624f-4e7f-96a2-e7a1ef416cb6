﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\NUnit3TestAdapter.3.16.1\build\net35\NUnit3TestAdapter.props" Condition="Exists('..\packages\NUnit3TestAdapter.3.16.1\build\net35\NUnit3TestAdapter.props')" />
  <Import Project="..\packages\NUnit.3.12.0\build\NUnit.props" Condition="Exists('..\packages\NUnit.3.12.0\build\NUnit.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A3F2454A-3A70-4A22-90ED-013FD118BAD5}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RoofSnap.WebAPI.Tests.Unit</RootNamespace>
    <AssemblyName>RoofSnap.WebAPI.Tests.Unit</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{3AC096D0-A1C2-E12C-1390-A8335801FDAB};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">15.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <ReferencePath>$(ProgramFiles)\Common Files\microsoft shared\VSTT\$(VisualStudioVersion)\UITestExtensionPackages</ReferencePath>
    <IsCodedUITest>False</IsCodedUITest>
    <TestProjectType>UnitTest</TestProjectType>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoMapper, Version=5.2.0.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.5.2.0\lib\net45\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="Braintree, Version=4.8.0.0, Culture=neutral, PublicKeyToken=31b586f34d3e96c7, processorArchitecture=MSIL">
      <HintPath>..\packages\Braintree.4.8.0\lib\net452\Braintree.dll</HintPath>
    </Reference>
    <Reference Include="Castle.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <HintPath>..\packages\Castle.Core.4.2.0\lib\net45\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="FluentAssertions, Version=5.9.0.0, Culture=neutral, PublicKeyToken=33f2691a05b67b6a, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentAssertions.5.9.0\lib\net47\FluentAssertions.dll</HintPath>
    </Reference>
    <Reference Include="Flurl, Version=2.5.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Flurl.2.5.0\lib\net40\Flurl.dll</HintPath>
    </Reference>
    <Reference Include="Flurl.Http, Version=2.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Flurl.Http.2.0.1\lib\net45\Flurl.Http.dll</HintPath>
    </Reference>
    <Reference Include="GeoAPI, Version=1.7.5.0, Culture=neutral, PublicKeyToken=a1a0da7def465678, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoAPI.Core.1.7.5\lib\net45\GeoAPI.dll</HintPath>
    </Reference>
    <Reference Include="GeoAPI.CoordinateSystems, Version=1.7.5.0, Culture=neutral, PublicKeyToken=a1a0da7def465678, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoAPI.CoordinateSystems.1.7.5\lib\net45\GeoAPI.CoordinateSystems.dll</HintPath>
    </Reference>
    <Reference Include="GeoJSON.Net, Version=0.1.51.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoJSON.Net.0.1.51\lib\portable-net40+sl5+wp80+win8+wpa81\GeoJSON.Net.dll</HintPath>
    </Reference>
    <Reference Include="GeoJSON.Net.Contrib.MsSqlSpatial, Version=0.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GeoJSON.Net.Contrib.MsSqlSpatial.0.2.0\lib\net45\GeoJSON.Net.Contrib.MsSqlSpatial.dll</HintPath>
    </Reference>
    <Reference Include="Handlebars, Version=1.8.1.0, Culture=neutral, PublicKeyToken=22225d0bf33cd661, processorArchitecture=MSIL">
      <HintPath>..\packages\Handlebars.Net.1.9.0\lib\net40\Handlebars.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.EventGrid, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.EventGrid.2.0.0\lib\net452\Microsoft.Azure.EventGrid.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Search, Version=10.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Search.10.1.0\lib\net461\Microsoft.Azure.Search.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Search.Common, Version=10.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Search.Common.10.1.0\lib\net461\Microsoft.Azure.Search.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Search.Data, Version=10.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Search.Data.10.1.0\lib\net461\Microsoft.Azure.Search.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Search.Service, Version=10.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Search.Service.10.1.0\lib\net461\Microsoft.Azure.Search.Service.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.EntityFrameworkCore, Version=2.2.1.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.EntityFrameworkCore.2.2.1\lib\netstandard2.0\Microsoft.EntityFrameworkCore.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.EntityFrameworkCore.Abstractions, Version=2.2.1.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.EntityFrameworkCore.Abstractions.2.2.1\lib\netstandard2.0\Microsoft.EntityFrameworkCore.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.EntityFrameworkCore.InMemory, Version=2.2.1.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.EntityFrameworkCore.InMemory.2.2.1\lib\netstandard2.0\Microsoft.EntityFrameworkCore.InMemory.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Abstractions, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Caching.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Caching.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Memory, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Caching.Memory.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Caching.Memory.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Binder.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.2.2.0\lib\net461\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Rest.ClientRuntime, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Rest.ClientRuntime.2.3.20\lib\net461\Microsoft.Rest.ClientRuntime.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Rest.ClientRuntime.Azure, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Rest.ClientRuntime.Azure.3.3.19\lib\net461\Microsoft.Rest.ClientRuntime.Azure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Spatial, Version=7.5.3.21218, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Spatial.7.5.3\lib\portable-net45+win8+wpa81\Microsoft.Spatial.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Types.14.0.1016.290\lib\net40\Microsoft.SqlServer.Types.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Win32.Primitives.4.3.0\lib\net46\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="Moq, Version=4.7.137.0, Culture=neutral, PublicKeyToken=69f491c39445e920, processorArchitecture=MSIL">
      <HintPath>..\packages\Moq.4.7.137\lib\net45\Moq.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite, Version=1.15.3.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.Core.1.15.3\lib\net45\NetTopologySuite.dll</HintPath>
    </Reference>
    <Reference Include="NetTopologySuite.CoordinateSystems, Version=1.15.3.0, Culture=neutral, PublicKeyToken=f580a05016ebada1, processorArchitecture=MSIL">
      <HintPath>..\packages\NetTopologySuite.CoordinateSystems.1.15.3\lib\net45\NetTopologySuite.CoordinateSystems.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Ninject, Version=3.3.4.0, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.3.3.4\lib\net45\Ninject.dll</HintPath>
    </Reference>
    <Reference Include="Ninject.MockingKernel, Version=3.3.0.0, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.MockingKernel.3.3.0\lib\net45\Ninject.MockingKernel.dll</HintPath>
    </Reference>
    <Reference Include="Ninject.MockingKernel.Moq, Version=3.3.0.0, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.MockingKernel.Moq.3.3.0\lib\net45\Ninject.MockingKernel.Moq.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.7.2\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework, Version=3.12.0.0, Culture=neutral, PublicKeyToken=2638cd05610744eb, processorArchitecture=MSIL">
      <HintPath>..\packages\NUnit.3.12.0\lib\net45\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.2.2.0\lib\net45\Remotion.Linq.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.Events.Core, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.Events.Core.1.0.314\lib\netstandard2.0\RoofSnap.Events.Core.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.Functions.Models, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.Functions.Models.1.0.18\lib\netstandard2.0\RoofSnap.Functions.Models.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.GeoCode.Client, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.GeoCode.Client.1.0.15\lib\netstandard2.0\RoofSnap.GeoCode.Client.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.GeoCode.Model, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.GeoCode.Model.1.0.15\lib\netstandard2.0\RoofSnap.GeoCode.Model.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.Measurements, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.Measurements.1.1.12\lib\netstandard2.0\RoofSnap.Measurements.dll</HintPath>
    </Reference>
    <Reference Include="RoofSnap.Search.Models, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RoofSnap.Search.Models.1.0.16\lib\netstandard2.0\RoofSnap.Search.Models.dll</HintPath>
    </Reference>
    <Reference Include="SalesforceMagic, Version=0.2.4.1, Culture=neutral, PublicKeyToken=null" />
    <Reference Include="System" />
    <Reference Include="System.AppContext, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.AppContext.4.3.0\lib\net463\System.AppContext.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.4.0\lib\netstandard2.0\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=1.2.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Collections.Immutable.1.5.0\lib\netstandard2.0\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.4.5.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Console, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Console.4.3.0\lib\net46\System.Console.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.4.5.0\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.Tracing.4.3.0\lib\net462\System.Diagnostics.Tracing.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Globalization.Calendars.4.3.0\lib\net46\System.Globalization.Calendars.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Interactive.Async, Version=3.2.0.0, Culture=neutral, PublicKeyToken=94bc3704cddfc263, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Interactive.Async.3.2.0\lib\net46\System.Interactive.Async.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.IO.Compression.ZipFile, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.ZipFile.4.3.0\lib\net46\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.4.3.0\lib\net46\System.IO.FileSystem.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.4.1.0\lib\net463\System.Linq.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.Expressions.4.1.0\lib\net463\System.Linq.Expressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.1\lib\netstandard2.0\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.Extensions, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Primitives, Version=4.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Net.Sockets, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Sockets.4.3.0\lib\net46\System.Net.Sockets.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors, Version=4.1.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.4.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.4.1.0\lib\net462\System.Reflection.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.2\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Extensions.4.1.0\lib\net462\System.Runtime.Extensions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.4.3.0\lib\net463\System.Runtime.InteropServices.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.RegularExpressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.RegularExpressions.4.3.0\lib\net463\System.Text.RegularExpressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.3\lib\netstandard2.0\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml.ReaderWriter, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Xml.ReaderWriter.4.3.0\lib\net46\System.Xml.ReaderWriter.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="WebApi.Hal, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\WebApi.Hal.2.6.0\lib\net45\WebApi.Hal.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Areas\Billing\BillingTransactionCallbackTests.cs" />
    <Compile Include="Areas\Billing\BillingTransactionPreSettleTests.cs" />
    <Compile Include="Areas\Billing\BillingTransactionPropertySetterTests.cs" />
    <Compile Include="Areas\Billing\BillingTransactionRefundTests.cs" />
    <Compile Include="Areas\Billing\BillingTransactionResultFactoryTests.cs" />
    <Compile Include="Areas\Billing\BillingTransactionSettleTests.cs" />
    <Compile Include="Areas\Billing\BillingTransactionVoidTests.cs" />
    <Compile Include="Areas\Billing\Receipts\SubscriptionReceiptsProcessorTests.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionReceiptMailToAddressesTests.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionReceiptBillingContactInfoTests.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionReceiptCancellationInfoTests.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionReceiptCompanyContactInfoTests.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionReceiptOrganizationInfoTests.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionReceiptSubscriptionInfoTests.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionReceiptBillingFrequencyDecoratorTests.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionReceiptCreditCardInfoTests.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionFailedBillingTests.cs" />
    <Compile Include="Areas\Billing\Receipts\TransactionReceiptTransactionInfoTests.cs" />
    <Compile Include="Areas\Documents\CreateDocumentRequestFactoryTests.cs" />
    <Compile Include="Areas\Documents\DocumentTemplateFactoryTests.cs" />
    <Compile Include="Areas\Documents\DocumentTemplateV2ServiceTests.cs" />
    <Compile Include="Areas\Documents\DocumentV2ServiceTests.cs" />
    <Compile Include="Areas\Documents\DocumentRenderingFileNameTests.cs" />
    <Compile Include="Areas\Organizations\OrganizationActivityDeterminerTests.cs" />
    <Compile Include="Areas\Organizations\OrganizationIdSecurityCheckerTests.cs" />
    <Compile Include="Areas\Organizations\OrganizationSketchOrderPriceGridFactoryTests.cs" />
    <Compile Include="Areas\Organizations\OrganizationTests.cs" />
    <Compile Include="Areas\Payments\PaymentsControllerTests.cs" />
    <Compile Include="Areas\Payments\StripePayment\StripeEventControllerTests.cs" />
    <Compile Include="Areas\Payments\StripePayment\StripeExtensionsTests.cs" />
    <Compile Include="Areas\Projects\EstimateTemplateClonerTests.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\MetalRoofReportTests.cs" />
    <Compile Include="Areas\Projects\Estimating\Reports\GutterReportTests.cs" />
    <Compile Include="Areas\Projects\ProjectServiceProjectShareTests.cs" />
    <Compile Include="Areas\Projects\Search\AzureProjectSearchFilterFactoryTests.cs" />
    <Compile Include="Areas\Projects\Search\AzureProjectSearchTests.cs" />
    <Compile Include="Areas\Projects\Search\ProjectSearchTests.cs" />
    <Compile Include="Areas\SketchOrders\GeocodingServiceTests.cs" />
    <Compile Include="Areas\SketchOrders\SketchOrderPaymentServiceTests.cs" />
    <Compile Include="Areas\Subscriptions\SubscriptionManagerTests.cs" />
    <Compile Include="AutoMapper\CreateAppVersionStatusDtoToDbAppStatusVersionTypeConverterTests.cs" />
    <Compile Include="AutoMapper\DbOfficeCategorySettingToCombinedCategoryProfileTests.cs" />
    <Compile Include="AutoMapper\DocumentTemplateDataProfileTests.cs" />
    <Compile Include="AutoMapper\Factories\DbEdgeFactoryTests.cs" />
    <Compile Include="AutoMapper\Factories\DbFaceEdgeTypeFactoryTests.cs" />
    <Compile Include="AutoMapper\Factories\DbFaceFactoryTests.cs" />
    <Compile Include="AutoMapper\Factories\EdgeRepresentationFactoryTests.cs" />
    <Compile Include="AutoMapper\Factories\FaceRepresentationFactoryTests.cs" />
    <Compile Include="AutoMapper\Factories\NearmapTokenProfileTests.cs" />
    <Compile Include="AutoMapper\Factories\PinRepresentationFactoryTests.cs" />
    <Compile Include="AutoMapper\Factories\VertexRepresentationFactoryTests.cs" />
    <Compile Include="AutoMapper\MeasurementsDtoToDbProjectMeasurementsTests.cs" />
    <Compile Include="AutoMapper\UserProfileProfile.cs" />
    <Compile Include="AutoMapper\ValidConfigurationTests.cs" />
    <Compile Include="AutoMapper\VertexProfileTests.cs" />
    <Compile Include="Common\Braintree\AddOns\UserAddOnsRequestFactoryTests.cs" />
    <Compile Include="Common\Braintree\BraintreeCustomerManagerTests.cs" />
    <Compile Include="Common\Data\RoofSnapDataFactoryTests.cs" />
    <Compile Include="Common\Data\Security\RoofSnapDataSecurityTests.cs" />
    <Compile Include="Common\Documents\DecoratorTest.cs" />
    <Compile Include="Common\Documents\DocumentStorageTests.cs" />
    <Compile Include="Common\Documents\DocumentDataDecoratorTests.cs" />
    <Compile Include="Common\Documents\OrganizationSettingsDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Billing\ProjectBillingDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Customer\ProjectCustomerDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Drawing\ProjectDrawingDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Images\ProjectImagesAspectDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Images\ProjectImagesDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Images\ProjectImagesDrawingDecoratorFromBlobTests.cs" />
    <Compile Include="Common\Documents\Project\Images\ProjectImagesDrawingDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Measurements\ProjectMeasurementsAreaDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Measurements\ProjectMeasurementsAreaPitchDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Measurements\ProjectMeasurementsDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Measurements\ProjectMeasurementsLinearEdgeTypesDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Measurements\ProjectMeasurementsLinearSubEdgeTypesTests.cs" />
    <Compile Include="Common\Documents\Project\Office\ProjectOfficeAddressDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Office\ProjectOfficeDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Owner\ProjectOwnerContactInfoDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\Owner\ProjectOwnerContactInfoNameDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\ProjectDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\ProjectNotesDecoratorTests.cs" />
    <Compile Include="Common\Documents\Project\SketchOrder\ProjectSketchOrderDecoratorTests.cs" />
    <Compile Include="Common\Documents\SketchOrderNotificationDocumentDataFactoryTests.cs" />
    <Compile Include="Common\WebApi\QueryableOrdererTests.cs" />
    <Compile Include="Common\WebApi\SortParameterFactoryTests.cs" />
    <Compile Include="Controllers\BraintreeWebhooksControllerTests.cs" />
    <Compile Include="Controllers\SignUpControllerTests.cs" />
    <Compile Include="Controllers\SketchOrdersControllerTests.cs" />
    <Compile Include="Controllers\UserProfilesControllerTests.cs" />
    <Compile Include="DbAsyncQueryProvider.cs" />
    <Compile Include="DbModels\DbVertexSerializationTest.cs" />
    <Compile Include="EF6MockDataMockExtensions.cs" />
    <Compile Include="EstimateEmails\EstimateEmailRenderingTests.cs" />
    <Compile Include="ExampleDbSetMockTest.cs" />
    <Compile Include="Factories\CreateEstimateItemStrategyFactoryTests.cs" />
    <Compile Include="Factories\DbProjectEstimateItemInsertableListsFactoryTests.cs" />
    <Compile Include="Factories\PinRepresentationFactoryTests.cs" />
    <Compile Include="Factories\ProjectEstimateItemInsertableFactoryTests.cs" />
    <Compile Include="IoCTests.cs" />
    <Compile Include="Method.cs" />
    <Compile Include="NTestScenario.cs" />
    <Compile Include="OrganizationFeatures\AddOnsOrganizationFeatureStrategyTests.cs" />
    <Compile Include="OrganizationFeatures\AllowSnappingGoogleImageryOrganizationFeatureStrategyTests.cs" />
    <Compile Include="OrganizationFeatures\DisableRushOrderingFeatureStrategyTests.cs" />
    <Compile Include="OrganizationFeatures\EstimatingOrganizationFeatureStrategyTests.cs" />
    <Compile Include="OrganizationFeatures\FreeSketchOrdersIncludedOrganizationFeatureStrategyTest.cs" />
    <Compile Include="OrganizationFeatures\SketchServiceOrganizationFeatureStrategyTests.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Common\Braintree\Mocks\TestSubscription.cs" />
    <Compile Include="Common\Braintree\Mocks\TestBraintreeTransaction.cs" />
    <Compile Include="PropertyEditPermissionTests.cs" />
    <Compile Include="Salesforce\SalesforceSynchronizationServiceTests.cs" />
    <Compile Include="Services\AppVersionStatusServiceTests.cs" />
    <Compile Include="Services\Braintree\BraintreeSubscriptionServiceTests.cs" />
    <Compile Include="Services\Braintree\BraintreeSubscriptionServiceTestsV2.cs" />
    <Compile Include="Services\DocumentServiceTests.cs" />
    <Compile Include="Services\DocumentTemplateV2ServiceTests.cs" />
    <Compile Include="Services\DocumentTemplateServiceTests.cs" />
    <Compile Include="Services\Estimates\CreateEstimateCustomItemStrategyTests.cs" />
    <Compile Include="Services\Estimates\EstimateItemFromEstimateGeneratorTests.cs" />
    <Compile Include="Services\Estimates\EstimateTotalCalculators\EstimateMarkupDiscountCalculatorTests.cs" />
    <Compile Include="Services\Estimates\EstimateTotalCalculators\EstimateMaterialCalculatorTests.cs" />
    <Compile Include="Services\Estimates\EstimateTotalCalculators\EstimateSubtotalCalculatorTests.cs" />
    <Compile Include="Services\Estimates\EstimateTotalCalculators\EstimateTaxCalculatorTests.cs" />
    <Compile Include="Services\Estimates\EstimateTotalCalculators\EstimateTotalCalculatorTests.cs" />
    <Compile Include="Services\Estimates\EstimateTotalCalculators\ItemTypeArrayFactoryTests.cs" />
    <Compile Include="Services\Estimates\EstimateTotalCalculators\TotalByItemTypeCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\ApronFlashingUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\DownSpoutsUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\EaveEdgeUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\GuttersUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\GutterToppersUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\HipAndRidgeCapUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\IceWaterShieldUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\LinearFeetUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\RakeEdgeUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\RidgeVentUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\StarterShinglesUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\StepFlashingUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\LinearFeetUnitQuantityFromCategoryCalculators\ValleysUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateChooserTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemBaseSettingsInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemColorIdInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemCoveragePerUnitInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemDescriptionInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemEstimateCategoryIdInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemHideOnContractInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemHideOnEstimateInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemHideOnLaborReportInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemHideOnMaterialOrderInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemImageInitializerTEsts.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemItemTypeInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemLaborCostInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemMaterialCostInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemMaterialOrderDescriptionInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemOrderInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemSystemChargeableItemIdInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemTotalPerUnitInitializerTest.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemInitializers\ProjectEstimateItemUnitsInitializerTests.cs" />
    <Compile Include="Services\Estimates\ProjectEstimateItemRules\CanDeleteProjectEstimateItemRuleTests.cs" />
    <Compile Include="Services\Estimates\SquaresUnitQuantityFromCategoryCalculators\LowSlopeRoofingUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\SquaresUnitQuantityFromCategoryCalculators\RemoveAndDisposeSecondLayerUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\SquaresUnitQuantityFromCategoryCalculators\RemoveAndDisposeShinglesUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\SquaresUnitQuantityFromCategoryCalculators\ShingleFastenersUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\SquaresUnitQuantityFromCategoryCalculators\SquaresUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\SquaresUnitQuantityFromCategoryCalculators\UnderlaymentUnitQuantityFromCategoryCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UniqueMaterialItemsPerEstimateCheckerTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromCategoryCalculatorsTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\ActualSquaresUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\ApronFlashingUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\DownSpoutsQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\EaveEdgeUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\EavesUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\GuttersUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\HipsUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\IceWaterShieldUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\LowSlopeUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\PitchChangeUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\RakeEdgeUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\RakesUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\RidgesUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\RidgeVentUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\SecondLayerUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\StepFlashingUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\StepUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\TotalSquaresFromMeasurementCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\TwoStoryUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\UnitQuantityFromMeasurementCalculatorTest.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\ValleysUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromMeasurementCalculators\WallUnitQuantityFromMeasurmentCalculatorTests.cs" />
    <Compile Include="Services\Estimates\UnitQuantityFromOfficePricedChargeableItemsAndProjectMeasurementsCalculatorTests.cs" />
    <Compile Include="Services\Estimates\WastePercentageCalculators\Scenarios\WastePercentageCalculatorOPCIHasCategoryAndAllowingWasteScenario.cs" />
    <Compile Include="Services\Estimates\WastePercentageCalculators\Scenarios\WastePercentageCalculatorOPCIHasCategoryAndAllowingWasteWithOfficeCategorySettingsScenario.cs" />
    <Compile Include="Services\Estimates\WastePercentageCalculators\Scenarios\WastePercentageCalculatorOPCIHasCategoryScenario.cs" />
    <Compile Include="Services\Estimates\WastePercentageCalculators\Scenarios\WastePercentageCalculatorOPCIHasOfficeCustomCategoryScenario.cs" />
    <Compile Include="Services\Estimates\WastePercentageCalculators\Scenarios\WastePercentageCalculatorOPCIHasOfficeCustomCategoryWithShingleParentScenario.cs" />
    <Compile Include="Services\Estimates\WastePercentageCalculators\Scenarios\WastePercentageCalculatorTestScenario.cs" />
    <Compile Include="Services\Estimates\WastePercentageCalculators\WastePercentageCalculatorOPCIHasCategoryAndAllowingWasteWithShingleParentAndProjectMeasurementsTests.cs" />
    <Compile Include="Services\Estimates\WastePercentageCalculators\WastePercentageCalculatorOPCIHasCategoryTests.cs" />
    <Compile Include="Services\Estimates\WastePercentageCalculators\WastePercentageCalculatorOPCIHasOfficeCustomCategoryTests.cs" />
    <Compile Include="Services\Estimates\WastePercentageCalculators\WastePercentageCalculatorOPCIHasOfficeCustomCategoryWithShingleParentAndProjectMeasurementsExistTests.cs" />
    <Compile Include="Services\Estimates\WastePercentageCalculators\WastePercentageCalculatorOPCIHasOfficeCustomCategoryWithShingleParentTests.cs" />
    <Compile Include="Services\Estimates\WastePercentageCalculators\WastePercentageCalculatorTests.cs" />
    <Compile Include="Services\FeaturesServiceTests.cs" />
    <Compile Include="Services\GoogleImageryDisablerTests.cs" />
    <Compile Include="Services\PremiumImageryAvailabilityServiceTests.cs" />
    <Compile Include="Services\NearmapTokenServiceTests.cs" />
    <Compile Include="Services\OrganizationServiceTests.cs" />
    <Compile Include="Services\ProjectFactoryTests.cs" />
    <Compile Include="Services\RoofSnapCreditTransactionatorTests.cs" />
    <Compile Include="Services\SketchOrderCostsCalculatorTests.cs" />
    <Compile Include="Services\UserProfiles\ActiveUsersCounterTests.cs" />
    <Compile Include="Services\UserProfiles\AddUserOfficeUserProfileFactoryDecoratorTests.cs" />
    <Compile Include="Services\UserProfiles\AddUserRolesUserProfileFactoryDecoratorTests.cs" />
    <Compile Include="Services\UserProfiles\CreateUserProfileServiceTests.cs" />
    <Compile Include="Services\UserProfiles\UserLimitCheckerTests.cs" />
    <Compile Include="Services\UserProfiles\UserProfileFactoryTests.cs" />
    <Compile Include="Services\UserProfiles\UserProfileServiceTests.cs" />
    <Compile Include="SketchOSWorkflow\DrawingRequestFactoryTests.cs" />
    <Compile Include="SketchOSWorkflow\EdgeSubTypeConverterTests.cs" />
    <Compile Include="SketchOSWorkflow\FaceTypeConverterTests.cs" />
    <Compile Include="SketchOSWorkflow\LockedDownSketchOrderTests.cs" />
    <Compile Include="SketchOSWorkflow\OfficeDefaultEdgeSubTypesApplicationMeasurementsHandlerTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderContextTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderDelivererTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderProjectCloningHandlerTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderProjectDefaultEdgeSubTypesApplicatorTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderStateTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderStateTests\BilledSketchOrderTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderStateTests\CancelledSketchOrderTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderStateTests\CompleteSketchOrderTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderStateTests\ExceptionSketchOrderTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderStateTests\IncompleteSketchOrderTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderStateTests\InProgressSketchOrderTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderStateTests\PendingRejectionSketchOrderTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderStateTests\PendingReviewSketchOrderTests.cs" />
    <Compile Include="SketchOSWorkflow\SketchOrderStateTests\QueuedSketchOrderTests.cs" />
    <Compile Include="SketchOSWorkflow\TypeAttributePropertiesTestData.cs" />
    <Compile Include="SketchServiceBilling\SketchOrderBillingQueryFactoryTests.cs" />
    <Compile Include="SketchServiceBilling\SketchOrderRefundInfoFactoryTests.cs" />
    <Compile Include="SketchServiceBilling\SketchOrderTransactionSummaryBuilderTests.cs" />
    <Compile Include="SqlServerTypes\Loader.cs" />
    <Compile Include="TestDatabase.cs" />
    <Compile Include="CircularReferenceRemovalTests.cs" />
    <Compile Include="UserProfileBilling\BraintreeSubscriptionIdFactoryTests.cs" />
    <Compile Include="UserProfileBilling\CustomerRequestFactoryTests.cs" />
    <Compile Include="UserProfileBilling\ExistingCustomerActiveSubscriptionUpdateStrategyTests.cs" />
    <Compile Include="UserProfileBilling\ExistingCustomerCancelledSubscriptionUpdateStrategyTests.cs" />
    <Compile Include="UserProfileBilling\ExistingCustomerPastDueSubscriptionUpdateStrategyTests.cs" />
    <Compile Include="UserProfileBilling\NewCustomerPaymentMethodUpdateStrategyTests.cs" />
    <Compile Include="UserProfileBilling\NewCustomerPaymentMethodUpdateStrategyTestScenario.cs" />
    <Compile Include="UserProfileBilling\NewCustomerSubscriptionUpdateStrategyTests.cs" />
    <Compile Include="UserProfileBilling\PaymentMethodUpdateStrategyFactoryTests.cs" />
    <Compile Include="UserProfileBilling\SubscriptionServiceTests.cs" />
    <Compile Include="UserProfileBilling\UpdatePaymentErrorModelFactoryTests.cs" />
    <Compile Include="UserProfileBilling\UserProfilePaymentServiceTests.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="SqlServerTypes\readme.htm" />
    <Content Include="SqlServerTypes\x64\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x64\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RoofSnap.Core.Data\RoofSnap.Core.Data.csproj">
      <Project>{2df72ba5-2df3-4284-932e-8c492813f0d2}</Project>
      <Name>RoofSnap.Core.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\RoofSnap.Core.Models\RoofSnap.Core.Models.csproj">
      <Project>{a018abb8-f981-4757-9af0-08d9e01cc1b2}</Project>
      <Name>RoofSnap.Core.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\RoofSnap.WebAPI\RoofSnap.WebAPI.csproj">
      <Project>{82e93ac9-0506-40de-aca6-18b9ffab9a48}</Project>
      <Name>RoofSnap.WebAPI</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="tests.runsettings" />
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\packages\FluentAssertions.Analyzers.0.11.4\analyzers\dotnet\cs\FluentAssertions.Analyzers.dll" />
  </ItemGroup>
  <Import Project="$(VSToolsPath)\TeamTest\Microsoft.TestTools.targets" Condition="Exists('$(VSToolsPath)\TeamTest\Microsoft.TestTools.targets')" />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\NUnit.3.12.0\build\NUnit.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NUnit.3.12.0\build\NUnit.props'))" />
    <Error Condition="!Exists('..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets'))" />
    <Error Condition="!Exists('..\packages\NUnit3TestAdapter.3.16.1\build\net35\NUnit3TestAdapter.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NUnit3TestAdapter.3.16.1\build\net35\NUnit3TestAdapter.props'))" />
  </Target>
  <Import Project="..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets" Condition="Exists('..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" />
  <Import Project="..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets" Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" />
</Project>