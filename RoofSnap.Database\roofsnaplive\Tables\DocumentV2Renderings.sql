﻿CREATE TABLE [roofsnaplive].[DocumentV2Renderings]
(
	[Id] INT NOT NULL PRIMARY KEY IDENTITY, 
    [ShortCode] AS ([hashids].[encode1]([Id])),
    [__createdAt] DATETIMEOFFSET(3) NOT NULL DEFAULT (CONVERT([datetimeoffset](3),sysutcdatetime(),(0))), 
	[__updatedAt] DATETIMEOFFSET(3) CONSTRAINT [DF_DocumentV2Renderings___updatedAt] DEFAULT SYSDATETIMEOFFSET() NOT NULL,
    [__deleted]                   BIT            DEFAULT ((0)) NOT NULL,
    [__version] ROWVERSION NOT NULL, 
	[DocumentId] INT NOT NULL,
    [BlobName] NVARCHAR(255) NULL, 
	[OrganizationId] BIGINT NOT NULL,
    [FileName] NVARCHAR(255) NOT NULL,
	[IsGenerated] BIT NOT NULL,
    [IsSigned] BIT NOT NULL DEFAULT 0,
    [ESignLink] NVARCHAR(255) NULL,
    CONSTRAINT [FK_DocumentsV2Renderings_DocumentsV2] FOREIGN KEY ([DocumentId]) REFERENCES [roofsnaplive].[documentsV2]([Id]), 
    CONSTRAINT [FK_DocumentsV2Renderings_Organizations] FOREIGN KEY ([OrganizationId]) REFERENCES [roofsnaplive].[Organizations]([id])
)

GO
CREATE TRIGGER roofsnaplive.TR_DocumentV2Renderings_InsertUpdate ON roofsnaplive.DocumentV2Renderings
AFTER INSERT, UPDATE
AS BEGIN
SET NOCOUNT ON
IF TRIGGER_NESTLEVEL(OBJECT_ID('roofsnaplive.TR_DocumentV2Renderings_InsertUpdate')) > 1 RETURN
IF UPDATE(__updatedAt) RETURN
UPDATE roofsnaplive.DocumentV2Renderings SET __updatedAt = CONVERT (DATETIMEOFFSET(3), SYSUTCDATETIME())
FROM roofsnaplive.DocumentV2Renderings t INNER JOIN INSERTED i on t.Id = i.id
END

GO
CREATE NONCLUSTERED INDEX [IX_DocumentV2Renderings_ShortCode] ON [roofsnaplive].[DocumentV2Renderings] ([__deleted], [ShortCode]);
GO

CREATE NONCLUSTERED INDEX [IX_DocumentV2Renderings_DocumentId] ON [roofsnaplive].[DocumentV2Renderings] ([DocumentId]);
GO

CREATE NONCLUSTERED INDEX [IX_DocumentV2Renderings_DocumentId_Deleted] ON [roofsnaplive].[DocumentV2Renderings] ([DocumentId], [__deleted]);
GO

CREATE NONCLUSTERED INDEX [IX_DocumentV2Renderings_ShortCode_Only] ON [roofsnaplive].[DocumentV2Renderings] ([ShortCode]);
GO

CREATE NONCLUSTERED INDEX [IX_DocumentV2Renderings_deleted] ON [roofsnaplive].[DocumentV2Renderings] ([__deleted] ASC) INCLUDE ([Id])