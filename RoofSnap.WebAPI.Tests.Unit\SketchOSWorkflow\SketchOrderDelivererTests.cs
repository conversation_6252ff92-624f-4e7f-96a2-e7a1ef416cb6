﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using RoofSnap.Core.Models;
using RoofSnap.WebAPI.Areas.Documents;
using RoofSnap.WebAPI.Areas.DocumentTemplates;
using RoofSnap.WebAPI.Areas.Offices.Estimating.Templates;
using RoofSnap.WebAPI.Areas.Projects.Estimating;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateOptions;
using RoofSnap.WebAPI.Areas.Estimating.EstimatesMailer;
using RoofSnap.WebAPI.Areas.Projects;
using RoofSnap.WebAPI.Areas.Projects.ProjectDocuments;
using RoofSnap.WebAPI.Areas.SketchOrders.Data;
using RoofSnap.WebAPI.Areas.SketchOrders.SketchOSWorkflow;
using RoofSnap.WebAPI.Common;
using RoofSnap.WebAPI.Common.Data;
using SketchOrderDeliveryOptions = RoofSnap.WebAPI.Areas.SketchOrders.Data.SketchOrderDeliveryOptions;
using HandlebarsDotNet;
using RoofSnap.WebAPI.Common.Wrappers;

namespace RoofSnap.WebAPI.Tests.Unit.SketchOSWorkflow
{
    [TestFixture]
    public class SketchOrderDelivererTests : NTestScenario<SketchOrderDeliverer>
    {
        private IRoofSnapLiveDbContext _dbContext;
        private Mock<IEstimateService> _mockEstimateService;

        private IQueryable<DbSketchOrder> SetupSketchOrders()
        {
            return new List<DbSketchOrder>
            {
                new DbSketchOrder
                {
                    Id = 1,
                    ProjectId = "123",
                    Organizationid = 456,
                    Projectname = "projectName",
                    DeliveryOptions = SketchOrderDeliveryOptions.OmitSketchReport
                },
                new DbSketchOrder
                {
                    Id = 2,
                    ProjectId = "456",
                    Organizationid = 789,
                    Projectname = "projectName2"
                },
                new DbSketchOrder
                {
                    Id = 3,
                    ProjectId = "1212",
                    Organizationid = 123,
                    Projectname = "projectName3",
                    SketchOrderStatus = SketchOrderStatusModel.Billed
                },
                new DbSketchOrder
                {
                    Id = 4,
                    ProjectId = "1213",
                    Organizationid = 120,
                    Projectname = "projectName4",
                    SketchOrderStatus = SketchOrderStatusModel.Complete
                }
            }.AsQueryable();
        }

        private IQueryable<DbEstimateTemplate> SetupDbEstimateTemplates()
        {
            return new List<DbEstimateTemplate>
            {
                new DbEstimateTemplate
                {
                    Id = "1",
                    OrganizationId = 123,
                    Name = "Test Thingy"
                },
                new DbEstimateTemplate
                {
                    Id = "2",
                    OrganizationId = 120,
                    Name = "Test Thingy 2"
                }
            }.AsQueryable();
        }

        protected override void PreSetup()
        {
            var mockContext = Kernel.GetMock<IRoofSnapLiveDbContext>();

            var mockSketchOrderData = SetupSketchOrders();
            mockContext.SetupIDbContextGet(mock => mock.SketchOrders, mockSketchOrderData);

            var mockEstimateTemplateData = SetupDbEstimateTemplates();
            mockContext.SetupIDbContextGet(mock => mock.EstimateTemplates, mockEstimateTemplateData);

            _dbContext = mockContext.Object;

            _mockEstimateService = Kernel.GetMock<IEstimateService>();

            _mockEstimateService.Setup(x =>
                x.CreateAsync(It.IsAny<string>(), It.IsAny<DbProjectEstimateOption>()))
                .ReturnsAsync(new DbProjectEstimateOption());
        }

        [Test]
        public async Task SketchOrderDelivererDeliversOmitsSketchReportIfOptionsSpecifyOmission()
        {
            var sketchOrder = _dbContext.SketchOrders.First(x => x.Id == 1);

            var documentGeneratorMock = Kernel.GetMock<IDocumentGenerator>();

            var sketchOrderResponseSender = Kernel.GetMock<ISketchOrderResponseSender>();
            sketchOrderResponseSender.Setup(m => m.AddPostCommitActionToSendEmail(It.IsAny<long>())).Verifiable();

            await UnitUnderTest.DeliverAsync(sketchOrder);

            documentGeneratorMock.Verify(m => m.GenerateSketchOrderSketchReportProjectDocumentAsync(It.IsAny<string>()),
                Times.Never);

            sketchOrderResponseSender.Verify(x => x.AddPostCommitActionToSendEmail(sketchOrder.Id));
        }

        [Test]
        public async Task SketchOrderDelivererDeliversProjectIfNotAlreadyCloned()
        {
            var expectedTemplate = new DocumentV2TemplateModel();
            var sketchOrder = _dbContext.SketchOrders.First(x => x.Id == 2);

            var sketchOrderProjectCloningHandlerMock =
                Kernel.GetMock<ISketchOrderProjectCloningHandler>();
            sketchOrderProjectCloningHandlerMock.Setup(m => m.ReassignProjectWithClonedProjectAsync(sketchOrder))
                .Returns(Task.CompletedTask)
                .Verifiable();

            var defaultEdgeSubTypesApplicatorMock =
                Kernel.GetMock<ISketchOrderProjectDefaultEdgeSubTypesApplicator>();
            defaultEdgeSubTypesApplicatorMock.Setup(m => m.SetDefaultEdgeSubTypesIfApplicableAsync(sketchOrder.Project, sketchOrder.SketchReportType))
                .Returns(Task.CompletedTask)
                .Verifiable();

            var documentV2Service = Kernel.GetMock<IDocumentsV2Service>();
            documentV2Service.Setup(m => m.CreateAsync(It.IsAny<DocumentV2Model>(), sketchOrder.ProjectId))
                .ReturnsAsync(new ServiceResult<DocumentV2Model>(new List<DocumentV2Model>().AsQueryable()))
                .Verifiable();
            documentV2Service.Setup(m => m.CreateRenderingAsync(It.IsAny<DocumentV2Model>(), null, null, false))
                .ReturnsAsync(new DocumentV2RenderingModel())
                .Verifiable();

            var documentsV2TemplateService = Kernel.GetMock<IDocumentTemplatesV2Service>();

            documentsV2TemplateService
                .Setup(x => x.GetSketchOrderReportTemplateForOrganization(sketchOrder.Organizationid))
                .ReturnsAsync(expectedTemplate);

            await UnitUnderTest.DeliverAsync(sketchOrder);

            documentsV2TemplateService.Verify(x =>
                x.GetSketchOrderReportTemplateForOrganization(sketchOrder.Organizationid));

            documentV2Service.Verify(x =>
                x.CreateAsync(
                    It.Is((DocumentV2Model y) =>
                        y.OrganizationId == sketchOrder.Organizationid && y.Template == expectedTemplate),
                    sketchOrder.ProjectId));

            documentV2Service.Verify(x =>
                x.CreateRenderingAsync(
                    It.Is((DocumentV2Model y) =>
                        y.OrganizationId == sketchOrder.Organizationid && y.Template == expectedTemplate), null, null, false));
        }

        [Test]
        public async Task SketchOrderDelivererDoesNotCloneProjectWhenCloneProjectIsFalse()
        {
            var expectedTemplate = new DocumentV2TemplateModel();
            var sketchOrder = _dbContext.SketchOrders.First(x => x.Id == 2);

            var sketchOrderProjectCloningHandlerMock =
                Kernel.GetMock<ISketchOrderProjectCloningHandler>();
            sketchOrderProjectCloningHandlerMock.Setup(m => m.ReassignProjectWithClonedProjectAsync(sketchOrder))
                .Returns(Task.CompletedTask)
                .Verifiable();

            var defaultEdgeSubTypesApplicatorMock =
                Kernel.GetMock<ISketchOrderProjectDefaultEdgeSubTypesApplicator>();
            defaultEdgeSubTypesApplicatorMock.Setup(m => m.SetDefaultEdgeSubTypesIfApplicableAsync(sketchOrder.Project, sketchOrder.SketchReportType))
                .Returns(Task.CompletedTask)
                .Verifiable();

            var documentV2Service = Kernel.GetMock<IDocumentsV2Service>();
            documentV2Service.Setup(m => m.CreateAsync(It.IsAny<DocumentV2Model>(), sketchOrder.ProjectId))
                .ReturnsAsync(new ServiceResult<DocumentV2Model>(new List<DocumentV2Model>().AsQueryable()))
                .Verifiable();
            documentV2Service.Setup(m => m.CreateRenderingAsync(It.IsAny<DocumentV2Model>(), null, null, false))
                .ReturnsAsync(new DocumentV2RenderingModel())
                .Verifiable();

            var documentsV2TemplateService = Kernel.GetMock<IDocumentTemplatesV2Service>();

            documentsV2TemplateService
                .Setup(x => x.GetSketchOrderReportTemplateForOrganization(sketchOrder.Organizationid))
                .ReturnsAsync(expectedTemplate);

            await UnitUnderTest.DeliverAsync(sketchOrder, false);

            documentsV2TemplateService.Verify(x =>
                x.GetSketchOrderReportTemplateForOrganization(sketchOrder.Organizationid));

            documentV2Service.Verify(x =>
                x.CreateAsync(
                    It.Is((DocumentV2Model y) =>
                        y.OrganizationId == sketchOrder.Organizationid && y.Template == expectedTemplate),
                    sketchOrder.ProjectId));

            documentV2Service.Verify(x =>
                x.CreateRenderingAsync(
                    It.Is((DocumentV2Model y) =>
                        y.OrganizationId == sketchOrder.Organizationid && y.Template == expectedTemplate), null, null, false));

            sketchOrderProjectCloningHandlerMock.Verify(x => x.ReassignProjectWithClonedProjectAsync(sketchOrder), Times.Never);
        }

        [TestCase(3, "Test Thingy")]
        [TestCase(4, "Test Thingy 2")]
        public async Task GivenFirstFreeSketchOrder_WhenDeliverAsync_ThenEstimateOptionsAreAddedToTheProject(long id,
            string name)
        {
            var sketchOrder = _dbContext.SketchOrders.First(x => x.Id == id);

            await UnitUnderTest.DeliverAsync(sketchOrder);

            _mockEstimateService.Verify(x => x.CreateAsync(sketchOrder.ProjectId,
                It.Is((DbProjectEstimateOption peo) => peo.Name == name)));

            Mock<IEstimateResponseSender> estimateResponseSender = Kernel.GetMock<IEstimateResponseSender>();
            estimateResponseSender.Setup(m => m.AddPostCommitActionToSendEmail(It.IsAny<string>())).Verifiable();

            estimateResponseSender.Verify(x => x.AddPostCommitActionToSendEmail(sketchOrder.ProjectId));
        }
    }
}