﻿using Ninject;
using Ninject.Activation;
using System;

namespace RoofSnap.WebAPI.Areas.Organizations.OrganizationFeatures
{
    public interface IOrganizationFeatureStrategyFactory
    {
        IOrganizationFeatureStrategy Create(OrganizationFeatureType featureType);
    }

    public class OrganizationFeatureStrategyFactory : IOrganizationFeatureStrategyFactory
    {
        private readonly IContext _context;

        public OrganizationFeatureStrategyFactory(IContext context)
        {
            _context = context;
        }

        public IOrganizationFeatureStrategy Create(OrganizationFeatureType featureType)
        {
            switch (featureType)
            {
                case OrganizationFeatureType.Estimating:
                    return _context.Kernel.Get<EstimatingOrganizationFeatureStrategy>();
                case OrganizationFeatureType.SketchService:
                    return _context.Kernel.Get<SketchServiceOrganizationFeatureStrategy>();
                case OrganizationFeatureType.AddOns:
                    return _context.Kernel.Get<AddOnsOrganizationFeatureStrategy>();
                case OrganizationFeatureType.AllowSnappingGoogleImagery:
                    return _context.Kernel.Get<AllowSnappingGoogleImageryOrganizationFeatureStrategy>();
                case OrganizationFeatureType.FreeSketchOrdersIncluded:
                    return _context.Kernel.Get<FreeSketchOrdersIncludedOrganizationFeatureStrategy>();
                case OrganizationFeatureType.RushOrdering:
                    return _context.Kernel.Get<RushOrderingFeatureStrategy>();
                case OrganizationFeatureType.EnableMetalRoof:
                    return _context.Kernel.Get<EnableMetalRoofOrganizationFeatureStrategy>();
                case OrganizationFeatureType.EnableGutters:
                    return _context.Kernel.Get<EnableGuttersOrganizationFeatureStrategy>();
                case OrganizationFeatureType.EnableEasierEstimates:
                    return _context.Kernel.Get<EnableEasierEstimatesOrganizationFeatureStrategy>();
                case OrganizationFeatureType.EnableSmartEstimates:
                    return _context.Kernel.Get<EnableSmartEstimatesOrganizationFeatureStrategy>();
                default:
                    throw new NotImplementedException($"Organization {featureType} has no strategy implementation");
            }
        }
    }
}