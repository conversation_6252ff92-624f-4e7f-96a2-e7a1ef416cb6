﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using RoofSnap.Core.Data;
using RoofSnap.WebAPI.Areas.CustomDocuments;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems.CreateEstimateItem.ProjectDrawing;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateOptions.EstimateTotalCalculators;
using RoofSnap.WebAPI.Areas.Projects.Estimating.UnitQuantityCalculation;
using RoofSnap.WebAPI.Areas.Projects.ProjectDocuments;
using RoofSnap.WebAPI.Areas.Projects.ProjectDrawings;
using RoofSnap.WebAPI.Areas.Projects.ProjectDrawings.Faces;
using RoofSnap.WebAPI.Areas.Projects.ProjectDrawings.Measurements;
using RoofSnap.WebAPI.Areas.Projects.ProjectImages;
using RoofSnap.WebAPI.Areas.Salesforce;
using RoofSnap.WebAPI.Areas.SketchOrders.Data;
using RoofSnap.WebAPI.Areas.SketchOrders.SketchOSWorkflow;
using RoofSnap.WebAPI.Areas.UserProfiles;
using RoofSnap.WebAPI.Common;
using RoofSnap.WebAPI.Common.BlobStorage;
using RoofSnap.WebAPI.Common.Config;
using RoofSnap.WebAPI.Common.Data;
using RoofSnap.WebAPI.Common.Events;
using RoofSnap.WebAPI.Common.Logging;
using RoofSnap.WebAPI.Common.WebAPI;
using RoofSnap.WebAPI.Common.Wrappers;
using RoofSnap.WebAPI.Common.Auth;
using RoofSnap.WebAPI.Areas.Projects.Estimating.Reports;
using RoofSnap.Core.Models;

namespace RoofSnap.WebAPI.Areas.Projects
{
    public partial class ProjectService : IProjectService
    {
        private readonly IDateTimeWrapper _dateTimeWrapper;
        private readonly IDateTimeOffsetWrapper _dateTimeOffsetWrapper;
        private readonly IErrorLogger _errorLogger;
        private readonly IEstimateTotalCalculator _estimateTotalCalculator;
        private readonly IProjectImageModelRuleEvaluator _projectImageModelRuleEvaluator;
        private readonly IBlobStorageFactory _blobStorageFactory;
        private readonly IOfficePricedChargeableItemsUnitsCalculator _officePricedChargeableItemsUnitsCalculator;
        private readonly IMapper _mapper;
        private const double DefaultWastePercentage = 10;
        private readonly IRoofSnapLiveDbContext _dbContext;
        private readonly IRoofSnapData _roofSnapData;
        private readonly IDocumentGenerator _documentGenerator;
        private readonly ISketchOrderCostCalculator _sketchOrderCostCalculator;
        private readonly IRoofSnapApiEventPublisher _roofSnapApiEventPublisher;
        private readonly IProjectCloner _projectCloner;
        private readonly IProjectDocumentService _projectDocumentService;
        private readonly ISalesforceSynchronizationService _salesforceSynchronizationService;
        private readonly IProjectDrawingEstimateItemSynchronizer _projectDrawingEstimateItemSynchronizer;
        private readonly IConfig _config;
        private readonly IGutterReportFactory _gutterReportFactory;

        public ProjectService(
            IDateTimeWrapper dateTimeWrapper,
            IDateTimeOffsetWrapper dateTimeOffsetWrapper,
            ProjectEstimateItemsFromProjectDrawingFactory projectEstimateItemsFromProjectDrawingFactory,
            IEstimateTotalCalculator estimateTotalCalculator,
            IErrorLogger errorLogger,
            IBlobStorageFactory blobStorageFactory,
            IProjectImageModelRuleEvaluator projectImageModelRuleEvaluator,
            IOfficePricedChargeableItemsUnitsCalculator officePricedChargeableItemsUnitsCalculator, 
            IMapper mapper,
            IRoofSnapLiveDbContext dbContext,
            IRoofSnapData roofSnapData,
            IDocumentGenerator documentGenerator,
            ISketchOrderCostCalculator sketchOrderCostCalculator,
            IFaceDeserializer faceDeserializer,
            IRoofSnapApiEventPublisher roofSnapApiEventPublisher, 
            IProjectCloner projectCloner,
            IProjectDocumentService projectDocumentService,
            ISalesforceSynchronizationService salesforceSynchronizationService,
            IProjectDrawingEstimateItemSynchronizer projectDrawingEstimateItemSynchronizer,
            IConfig config,
            IRoofSnapAuth roofSnapAuth,
            IUserProfileService userProfileService,
            IGutterReportFactory gutterReportFactory
            )
        {
            _dateTimeWrapper = dateTimeWrapper;
            _dateTimeOffsetWrapper = dateTimeOffsetWrapper;
            _estimateTotalCalculator = estimateTotalCalculator;
            _errorLogger = errorLogger;
            _projectImageModelRuleEvaluator = projectImageModelRuleEvaluator;
            _blobStorageFactory = blobStorageFactory;
            _officePricedChargeableItemsUnitsCalculator = officePricedChargeableItemsUnitsCalculator;
            _mapper = mapper;
            _dbContext = dbContext;
            _roofSnapData = roofSnapData;
            _documentGenerator = documentGenerator;
            _sketchOrderCostCalculator = sketchOrderCostCalculator;
            _faceDeserializer = faceDeserializer;
            _roofSnapApiEventPublisher = roofSnapApiEventPublisher;
            _projectCloner = projectCloner;
            _projectDocumentService = projectDocumentService;
            _salesforceSynchronizationService = salesforceSynchronizationService;
            _projectDrawingEstimateItemSynchronizer = projectDrawingEstimateItemSynchronizer;
            _config = config;
            _roofSnapAuth = roofSnapAuth;
            _userProfileService = userProfileService;
            _gutterReportFactory = gutterReportFactory;
        }

        public async Task<CombinedProjectDrawing> GetProjectDrawingAsync(string projectIdOrShortCode)
        {
            int? eaveRunsCount = null;
            DbProject project = await _dbContext.Projects
                .Include(p => p.ProjectMeasurements)
                .Include(p => p.ProjectDrawings)
                .FirstOrDefaultAsync(p => p.Id == projectIdOrShortCode || p.ShortCode == projectIdOrShortCode);

            DbProjectDrawing dbDrawing = project?.ProjectDrawings.FirstOrDefault();

            if (dbDrawing == null)
            {
                var defualtProjectDrawing = CreateDefaultCombinedProjectDrawing(projectIdOrShortCode);
                await CreateProjectDrawingAsync(projectIdOrShortCode, defualtProjectDrawing);
                dbDrawing = defualtProjectDrawing.Drawing;
            }

            DbProjectMeasurements dbMeasurements = project.ProjectMeasurements;

            //get the sketch order to check order type
            DbSketchOrder sketchOrder = await _dbContext.Entry(project)
                    .Collection(p => p.SketchOrders)
                    .Query()
                    .FirstOrDefaultAsync();

            if (sketchOrder != null)
            {
                if (sketchOrder.SketchReportType == DbSketchReportType.Gutters)
                {
                    GutterReport gutterReport = _gutterReportFactory.Create(project.Id);

                    var eaveRuns = await gutterReport.GetProjectEaveRunLengths();
                    eaveRunsCount = eaveRuns.Count();
                }
            }

            return new CombinedProjectDrawing
            {
                Drawing = dbDrawing,
                Measurements = dbMeasurements,
                OverrideDrawingMeasurements = project.OverrideDrawingMeasurements,
                Scalor = project.Scalor,
                OrganizationId = project.OrganizationId,
                GutterEaveRunsCount = eaveRunsCount
            };
        }

        public CombinedProjectDrawing CreateDefaultCombinedProjectDrawing(string projectIdOrShortCode)
        {
            return new CombinedProjectDrawing
            {
                Drawing = new DbProjectDrawing
                {
                    Projectid = projectIdOrShortCode,
                    Edges = "[]",
                    Faces = "[]",
                    Vertexes = "[]",
                    FaceEdges = "[]"
                },
                Measurements = new DbProjectMeasurements
                {
                    ActualSquares = 0,
                    WastePercentage = 10,
                    TotalSquares = 0,
                    LowSlope = 0,
                    TwoStory = 0,
                    SecondLayer = 0,
                    MetalRoof = 0,
                    Eaves = 0,
                    Rakes = 0,
                    Ridges = 0,
                    Hips = 0,
                    Valleys = 0,
                    Step = 0,
                    Wall = 0,
                    PitchChange = 0,
                    IceWaterShield = 0,
                    RidgeVent = 0,
                    RakeEdge = 0,
                    EaveEdge = 0,
                    StepFlashing = 0,
                    ApronFlashing = 0,
                    Gutters = 0,
                    GutterToppers = 0,
                    DownSpouts = 0,
                    Pitch1In12 = 0,
                    Pitch2In12 = 0,
                    Pitch3In12 = 0,
                    Pitch4In12 = 0,
                    Pitch5In12 = 0,
                    Pitch6In12 = 0,
                    Pitch7In12 = 0,
                    Pitch8In12 = 0,
                    Pitch9In12 = 0,
                    Pitch10In12 = 0,
                    Pitch11In12 = 0,
                    Pitch12In12 = 0,
                    Pitch0In12 = 0,
                    Pitch13In12 = 0,
                    Pitch14In12 = 0,
                    Pitch15In12 = 0,
                    Pitch16In12 = 0,
                    Pitch17In12 = 0,
                    Pitch18In12 = 0,
                    Pitch19In12 = 0,
                    Pitch20In12 = 0,
                    Pitch21In12 = 0,
                    Pitch22In12 = 0,
                    Pitch23In12 = 0,
                    Pitch24In12 = 0,
                    StarterOnEavesOnly = false
                },
                OverrideDrawingMeasurements = false,
                Scalor = 0
            };
        }

        public async Task<IList<DbProjectDocument>> GetProjectDocumentsAsync(string projectIdOrShortCode, PagingInfo pagingInfo, bool filterV2Copies = false)
        {
            DbProject project = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);

            if (project == null)
                throw new EntityNotFoundException<DbProject>(projectIdOrShortCode);

            IQueryable<DbProjectDocument> baseQuery = _dbContext.Entry(project)
                .Collection(p => p.ProjectDocuments)
                .Query();

            if (filterV2Copies)
                baseQuery = baseQuery.Where(x => !x.IsV2Copy);

            return await baseQuery
                .OrderBy(x => x.Category)
                .ThenBy(x => x.Name)
                .AsPagedAsync(pagingInfo);
        }

        public async Task<CombinedProjectDrawing> CreateProjectDrawingAsync(string projectIdOrShortCode, CombinedProjectDrawing newProjectDrawing)
        {
            DbProject project = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);
            
            DbProjectDrawing newDrawing = newProjectDrawing.Drawing;
            DateTimeOffset nowOffset = _dateTimeOffsetWrapper.Now;
            newDrawing.CreatedAt = nowOffset;
            newDrawing.UpdatedAt = nowOffset;

            //// Temporary code to allow what should have been a 409
            foreach (DbProjectDrawing projectDrawing in project.ProjectDrawings.ToArray())
            {
                _dbContext.ProjectDrawings.Remove(projectDrawing);
            }
            if (project.ProjectMeasurements != null)
            {
                _dbContext.ProjectMeasurements.Remove(project.ProjectMeasurements);
            }
            //// Temporary code to allow what should have been a 409

            project.ProjectDrawings.Add(newDrawing);         

            DbProjectMeasurements newMeasurements = newProjectDrawing.Measurements;
            if (newMeasurements != null)
            {
                DateTime now = _dateTimeWrapper.Now();

                newMeasurements.CreatedAt = now;
                newMeasurements.UpdatedAt = now;
                if(newMeasurements.WastePercentage == null)
                    newMeasurements.WastePercentage = DefaultWastePercentage;

                RoundMeasurements(newMeasurements, newProjectDrawing.OverrideDrawingMeasurements);
            }

            
            project.ProjectMeasurements = newMeasurements;

            project.OverrideDrawingMeasurements = newProjectDrawing.OverrideDrawingMeasurements ?? false;

            newProjectDrawing.Drawing.Project = project;
            await _projectDrawingEstimateItemSynchronizer.SyncProjectEstimateItemsToProjectDrawingAsync(project.Id, newProjectDrawing);
            
            await _dbContext.SaveChangesAsync();

            await UpdateAssociatedSketchOrderIfNeededAsync(project);

            return newProjectDrawing;
        }

        public async Task<CombinedProjectDrawing> UpdateProjectDrawingAsync(string projectIdOrShortCode, CombinedProjectDrawing combinedProjectDrawing, byte[] currentEtag)
        {
            DateTimeOffset nowOffset = _dateTimeOffsetWrapper.Now;
            DateTime now = _dateTimeWrapper.Now();
            combinedProjectDrawing.Drawing.UpdatedAt = nowOffset;
            _dbContext.SetVersion(combinedProjectDrawing.Drawing, currentEtag);

            if (combinedProjectDrawing.Measurements != null)
                combinedProjectDrawing.Measurements.UpdatedAt = now;

            UpdateMeasurements(combinedProjectDrawing, now);

            await _projectDrawingEstimateItemSynchronizer.SyncProjectEstimateItemsToProjectDrawingAsync(projectIdOrShortCode, combinedProjectDrawing);

            await _dbContext.SaveChangesAsync();

            await UpdateAssociatedSketchOrderIfNeededAsync(combinedProjectDrawing.Drawing.Project);

            return combinedProjectDrawing;
        }

        private async Task UpdateAssociatedSketchOrderIfNeededAsync(DbProject project)
        {
            // If this project drawing has an unfinished sketch order update its tier/cost
            DbSketchOrder sketchOrder = await _dbContext.Entry(project)
                .Collection(p => p.SketchOrders)
                .Query()
                .Where(so => so.Completeddate == null) // not completed yet
                .FirstOrDefaultAsync();
            if (sketchOrder != null)
            {
                var sketchOrderTier = await _sketchOrderCostCalculator.GetSketchOrderTierAsync(sketchOrder);
                sketchOrder.SketchOrderTierId = sketchOrderTier.Id;
                await _dbContext.SaveChangesAsync();
                sketchOrder.Cost = await _sketchOrderCostCalculator.CalculateSketchOrderCostAsync(sketchOrder);
                _roofSnapApiEventPublisher.PublishSketchOrderUpdatedEvent(sketchOrder);
            }
            else 
            {
                _roofSnapApiEventPublisher.PublishSketchOrderUpdatedEvent(project);
            }
        }

        private void UpdateMeasurements(CombinedProjectDrawing combinedProjectDrawing, DateTime now)
        {
            // If measurements were removed, delete any measurements from the database
            if (combinedProjectDrawing.Measurements == null &&
                combinedProjectDrawing.Drawing.Project.ProjectMeasurements != null)
                _dbContext.ProjectMeasurements.Remove(combinedProjectDrawing.Drawing.Project.ProjectMeasurements);

            RoundMeasurements(combinedProjectDrawing.Measurements, combinedProjectDrawing.OverrideDrawingMeasurements);

            // If measurements were added, add them to the database.
            if (combinedProjectDrawing.Measurements != null &&
                combinedProjectDrawing.Drawing.Project.ProjectMeasurements == null)
            {
                combinedProjectDrawing.Measurements.Id = combinedProjectDrawing.Drawing.Projectid;
                combinedProjectDrawing.Measurements.CreatedAt = now;
                if (combinedProjectDrawing.Measurements.WastePercentage == null)
                    combinedProjectDrawing.Measurements.WastePercentage = DefaultWastePercentage;
                _dbContext.ProjectMeasurements.Add(combinedProjectDrawing.Measurements);
            }
        }

        public async Task UpdateProjectOwnerAsync(string currentProjectOwner, string newProjectOwner)
        {
            if (!await _dbContext.UserProfiles.AnyAsync(userProfile => userProfile.UserName == newProjectOwner))
                throw new EntityNotFoundException<DbUserProfile>(newProjectOwner);

            if (!await _dbContext.Projects.AnyAsync(project => project.ProjectOwner == currentProjectOwner))
                throw new CurrentUsernameDoesNotHaveProjectsException();

            IEnumerable<DbProject> projects =
                _dbContext.Projects.Where(project => project.ProjectOwner == currentProjectOwner);

            foreach (DbProject project in projects)
            {
                project.ProjectOwner = newProjectOwner;
            }
        }

        public async Task DeleteProjectAsync(string projectIdOrShortCode)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);

            if (dbProject != null)
            {
                _dbContext.Projects.Remove(dbProject);
            }
        }

        public async Task<IList<DbDocument>> GetDocumentsAsync(string projectIdOrShortCode, PagingInfo pagingInfo)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);

            IList<DbDocument> documents = await _dbContext
                .Entry(dbProject)
                .Collection(p => p.Documents)
                .Query()
                .OrderBy(x => x.CreatedAt)
                .AsPagedAsync(pagingInfo);

            return documents;
        }

        private async Task<Uri> SaveProjectImageToBlobStorageAsync(string projectId, string imageKey, ProjectImageDto projectImageDto)
        {
            IBlobStorageWriteWrapper storage = await _blobStorageFactory.CreateProjectContainerForWriteAsync(projectId);
            Uri imageUri;

            using (var ms = new MemoryStream(projectImageDto.Image))
            {
                imageUri = await storage.UploadFromStreamAsync(imageKey, ms, projectImageDto.MimeType);
            }

            return imageUri;
        }

        public async Task<DbProjectDocument> GenerateProjectDocument(string projectIdOrShortCode, ProjectDocumentCategory projectDocumentCategory)
        {
            if (projectDocumentCategory == ProjectDocumentCategory.None)
                return null;

            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);

            if (dbProject == null)
                return null;

            await _documentGenerator.GenerateProjectDocumentAsync(dbProject.Id, projectDocumentCategory);

            DbProjectDocument dbProjectDocument = await _dbContext
                .Entry(dbProject)
                .Collection(p => p.ProjectDocuments)
                .Query()
                .OrderByDescending(projectDocument => projectDocument.CreatedAt)
                .FirstOrDefaultAsync(projectDocument =>
                    projectDocument.Category.Equals(projectDocumentCategory.ToString(), StringComparison.CurrentCultureIgnoreCase));

            return dbProjectDocument;
        }

        private void RoundMeasurements(DbProjectMeasurements measurements, bool? overrideProjectMeasurements)
        {
            if (overrideProjectMeasurements == true)
                return;

            if (measurements == null)
                return;

            measurements.ActualSquares = Round(measurements.ActualSquares, 2);
            measurements.WastePercentage = Round(measurements.WastePercentage, 2);
            measurements.TotalSquares = Round(measurements.TotalSquares, 2);
            measurements.LowSlope = Round(measurements.LowSlope, 2);
            measurements.TwoStory = Round(measurements.TwoStory, 2);
            measurements.SecondLayer = Round(measurements.SecondLayer, 2);

            measurements.Eaves = Round(measurements.Eaves, 1);
            measurements.Rakes = Round(measurements.Rakes, 1);
            measurements.Ridges = Round(measurements.Ridges, 1);
            measurements.Hips = Round(measurements.Hips, 1);
            measurements.Valleys = Round(measurements.Valleys, 1);
            measurements.Step = Round(measurements.Step, 1);
            measurements.Wall = Round(measurements.Wall, 1);
            measurements.PitchChange = Round(measurements.PitchChange, 1);
            measurements.IceWaterShield = Round(measurements.IceWaterShield, 1);
            measurements.RidgeVent = Round(measurements.RidgeVent, 1);
            measurements.RakeEdge = Round(measurements.RakeEdge, 1);
            measurements.EaveEdge = Round(measurements.EaveEdge, 1);
            measurements.StepFlashing = Round(measurements.StepFlashing, 1);
            measurements.ApronFlashing = Round(measurements.ApronFlashing, 1);
            measurements.Gutters = Round(measurements.Gutters, 1);
            measurements.GutterToppers = Round(measurements.GutterToppers, 1);
            measurements.DownSpouts = Round(measurements.DownSpouts, 1);

            measurements.Pitch0In12 = Round(measurements.Pitch0In12, 2);
            measurements.Pitch1In12 = Round(measurements.Pitch1In12, 2);
            measurements.Pitch2In12 = Round(measurements.Pitch2In12, 2);
            measurements.Pitch3In12 = Round(measurements.Pitch3In12, 2);
            measurements.Pitch4In12 = Round(measurements.Pitch4In12, 2);
            measurements.Pitch5In12 = Round(measurements.Pitch5In12, 2);
            measurements.Pitch6In12 = Round(measurements.Pitch6In12, 2);
            measurements.Pitch7In12 = Round(measurements.Pitch7In12, 2);
            measurements.Pitch8In12 = Round(measurements.Pitch8In12, 2);
            measurements.Pitch9In12 = Round(measurements.Pitch9In12, 2);
            measurements.Pitch10In12 = Round(measurements.Pitch10In12, 2);
            measurements.Pitch11In12 = Round(measurements.Pitch11In12, 2);
            measurements.Pitch12In12 = Round(measurements.Pitch12In12, 2);
            measurements.Pitch13In12 = Round(measurements.Pitch13In12, 2);
            measurements.Pitch14In12 = Round(measurements.Pitch14In12, 2);
            measurements.Pitch15In12 = Round(measurements.Pitch15In12, 2);
            measurements.Pitch16In12 = Round(measurements.Pitch16In12, 2);
            measurements.Pitch17In12 = Round(measurements.Pitch17In12, 2);
            measurements.Pitch18In12 = Round(measurements.Pitch18In12, 2);
            measurements.Pitch19In12 = Round(measurements.Pitch19In12, 2);
            measurements.Pitch20In12 = Round(measurements.Pitch20In12, 2);
            measurements.Pitch21In12 = Round(measurements.Pitch21In12, 2);
            measurements.Pitch22In12 = Round(measurements.Pitch22In12, 2);
            measurements.Pitch23In12 = Round(measurements.Pitch23In12, 2);
            measurements.Pitch24In12 = Round(measurements.Pitch24In12, 2);
        }

        private static double? Round(double? value, int decimals)
        {
            if (value == null)
                return null;

            return Math.Round(value.Value, decimals);
        }

        public async Task<IList<DbSketchOrder>> GetSketchOrders(PagingInfo pagingInfo, string projectIdOrShortCode)
        {
            DbProject dbProject = await _dbContext
                .Projects
                .GetByIdOrShortCodeAsync(projectIdOrShortCode);

            if (dbProject == null)
                return null;

            return await _dbContext.Entry(dbProject)
                .Collection(project => project.SketchOrders)
                .Query()
                .OrderByDescending(sketchOrder => sketchOrder.CreatedAt)
                .AsPagedAsync(pagingInfo);
        }

        public async Task<bool> SaveProjectAddressInfo(ProjectModel project, AddressInfo addressInfo)
        {
            try
            {
                project.CustomerAddress = addressInfo.CustomerAddress;
                project.CustomerCity = addressInfo.CustomerCity;
                project.CustomerState = addressInfo.CustomerState;
                project.CustomerZip = addressInfo.CustomerZip;
                project.MapCenterLat = addressInfo.MapCenterLat;
                project.MapCenterLon = addressInfo.MapCenterLon;
                await _roofSnapData.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}