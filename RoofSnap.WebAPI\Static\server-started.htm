﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>DEV Server Started</title>
</head>
<body>
    <div><h1>Listening...</h1></div>
    <style type="text/css">
        body {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-content: center;
            align-items: center;
            justify-content: center;
            margin: 0;
            background: #FFFFFF;
        }

        div {
            display: flex;
            align-content: center;
            align-items: center;
            justify-content: center;
            width: 300px;
            height: 300px;
            background-color: #21d4fd;
            background-image: linear-gradient(19deg, #21d4fd 0%, #b721ff 100%);
            border-radius: 30% 70% 70% 30%/30% 30% 70% 70%;
            box-shadow: 15px 15px 50px rgba(0,0,0,0.2);
            animation: morphing 20s ease-in-out infinite;
        }

        h1 {
            color: #fff;
            font-family: monospace, sans-serif;
            font-weight: 400;
            font-size: 35px;
            line-height: 100%;
            margin: auto;
            padding: 50px;
            text-align: center;
        }

        @keyframes morphing {
            0%, 100% {
                border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
                box-shadow: 15px 15px 50px rgba(0, 0, 0, 0.2);
            }

            10% {
                border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
            }

            20% {
                border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
                box-shadow: -10px -5px 50px rgba(0, 0, 0, 0.2);
            }

            30% {
                border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
            }

            40% {
                border-radius: 60% 40% 60% 40% / 60% 40% 60% 40%;
                box-shadow: 0px 20px 40px rgba(0, 0, 0, 0.2);
            }

            50% {
                border-radius: 25% 75% 35% 65% / 50% 50% 50% 50%;
            }

            60% {
                border-radius: 45% 55% 65% 35% / 70% 30% 40% 60%;
                box-shadow: -15px 10px 30px rgba(0, 0, 0, 0.2);
            }

            70% {
                border-radius: 40% 60% 60% 40% / 30% 70% 70% 30%;
            }

            80% {
                border-radius: 55% 45% 35% 65% / 60% 40% 60% 40%;
                box-shadow: 10px -15px 50px rgba(0, 0, 0, 0.2);
            }

            90% {
                border-radius: 50% 50% 50% 50% / 50% 50% 50% 50%;
            }
        }
    </style>
</body>
</html>