﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AutoMapper" version="5.2.0" targetFramework="net472" />
  <package id="Braintree" version="4.8.0" targetFramework="net472" />
  <package id="Castle.Core" version="4.2.0" targetFramework="net472" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net472" />
  <package id="FluentAssertions" version="5.9.0" targetFramework="net472" />
  <package id="FluentAssertions.Analyzers" version="0.11.4" targetFramework="net472" />
  <package id="Flurl" version="2.5.0" targetFramework="net472" />
  <package id="Flurl.Http" version="2.0.1" targetFramework="net472" />
  <package id="GeoAPI.CoordinateSystems" version="1.7.5" targetFramework="net472" />
  <package id="GeoAPI.Core" version="1.7.5" targetFramework="net472" />
  <package id="GeoJSON.Net" version="0.1.51" targetFramework="net472" />
  <package id="GeoJSON.Net.Contrib.MsSqlSpatial" version="0.2.0" targetFramework="net472" />
  <package id="Handlebars.Net" version="1.9.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.3" targetFramework="net472" />
  <package id="Microsoft.Azure.EventGrid" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Azure.Search" version="10.1.0" targetFramework="net472" />
  <package id="Microsoft.Azure.Search.Common" version="10.1.0" targetFramework="net472" />
  <package id="Microsoft.Azure.Search.Data" version="10.1.0" targetFramework="net472" />
  <package id="Microsoft.Azure.Search.Service" version="10.1.0" targetFramework="net472" />
  <package id="Microsoft.Bcl" version="1.1.10" targetFramework="net472" />
  <package id="Microsoft.Bcl.Build" version="1.0.21" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.Abstractions" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.Analyzers" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.EntityFrameworkCore.InMemory" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.Extensions.Caching.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Caching.Memory" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Net.Http" version="2.2.29" targetFramework="net472" />
  <package id="Microsoft.NETCore.Platforms" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.Rest.ClientRuntime" version="2.3.20" targetFramework="net472" />
  <package id="Microsoft.Rest.ClientRuntime.Azure" version="3.3.19" targetFramework="net472" />
  <package id="Microsoft.Spatial" version="7.5.3" targetFramework="net472" />
  <package id="Microsoft.SqlServer.Types" version="14.0.1016.290" targetFramework="net472" />
  <package id="Microsoft.Win32.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="Moq" version="4.7.137" targetFramework="net472" />
  <package id="NETStandard.Library" version="2.0.3" targetFramework="net472" />
  <package id="NetTopologySuite" version="1.15.3" targetFramework="net472" />
  <package id="NetTopologySuite.CoordinateSystems" version="1.15.3" targetFramework="net472" />
  <package id="NetTopologySuite.Core" version="1.15.3" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net472" />
  <package id="Ninject" version="3.3.4" targetFramework="net472" />
  <package id="Ninject.MockingKernel" version="3.3.0" targetFramework="net472" />
  <package id="Ninject.MockingKernel.Moq" version="3.3.0" targetFramework="net472" />
  <package id="NLog" version="4.7.2" targetFramework="net472" />
  <package id="NUnit" version="3.12.0" targetFramework="net472" />
  <package id="NUnit3TestAdapter" version="3.16.1" targetFramework="net472" developmentDependency="true" />
  <package id="Remotion.Linq" version="2.2.0" targetFramework="net472" />
  <package id="RoofSnap.Events.Core" version="1.0.314" targetFramework="net472" />
  <package id="RoofSnap.Functions.Models" version="1.0.18" targetFramework="net472" />
  <package id="RoofSnap.GeoCode.Client" version="1.0.15" targetFramework="net472" />
  <package id="RoofSnap.GeoCode.Model" version="1.0.15" targetFramework="net472" />
  <package id="RoofSnap.Measurements" version="1.1.12" targetFramework="net472" />
  <package id="RoofSnap.Search.Models" version="1.0.16" targetFramework="net472" />
  <package id="System.AppContext" version="4.3.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net472" />
  <package id="System.Collections" version="4.0.11" targetFramework="net472" />
  <package id="System.Collections.Concurrent" version="4.3.0" targetFramework="net472" />
  <package id="System.Collections.Immutable" version="1.5.0" targetFramework="net472" />
  <package id="System.ComponentModel.Annotations" version="4.5.0" targetFramework="net472" />
  <package id="System.Console" version="4.3.0" targetFramework="net472" />
  <package id="System.Diagnostics.Debug" version="4.0.11" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.5.0" targetFramework="net472" />
  <package id="System.Diagnostics.Tools" version="4.3.0" targetFramework="net472" />
  <package id="System.Diagnostics.Tracing" version="4.3.0" targetFramework="net472" />
  <package id="System.Globalization" version="4.3.0" targetFramework="net472" />
  <package id="System.Globalization.Calendars" version="4.3.0" targetFramework="net472" />
  <package id="System.Interactive.Async" version="3.2.0" targetFramework="net472" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Compression.ZipFile" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Linq" version="4.1.0" targetFramework="net472" />
  <package id="System.Linq.Expressions" version="4.1.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.1" targetFramework="net472" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net472" />
  <package id="System.Net.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Net.Sockets" version="4.3.0" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.4.0" targetFramework="net472" />
  <package id="System.ObjectModel" version="4.0.12" targetFramework="net472" />
  <package id="System.Reflection" version="4.1.0" targetFramework="net472" />
  <package id="System.Reflection.Extensions" version="4.0.1" targetFramework="net472" />
  <package id="System.Reflection.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Resources.ResourceManager" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime" version="4.3.1" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.2" targetFramework="net472" />
  <package id="System.Runtime.Extensions" version="4.1.0" targetFramework="net472" />
  <package id="System.Runtime.Handles" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.Numerics" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.RegularExpressions" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading" version="4.0.11" targetFramework="net472" />
  <package id="System.Threading.Tasks" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.3" targetFramework="net472" />
  <package id="System.Threading.Timer" version="4.3.0" targetFramework="net472" />
  <package id="System.Xml.ReaderWriter" version="4.3.0" targetFramework="net472" />
  <package id="System.Xml.XDocument" version="4.3.0" targetFramework="net472" />
  <package id="WebApi.Hal" version="2.6.0" targetFramework="net472" />
</packages>