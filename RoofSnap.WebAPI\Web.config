﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
	<configSections>
		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
		<section name="RaygunSettings" type="Mindscape.Raygun4Net.RaygunSettings, Mindscape.Raygun4Net" />
		<!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
		<section name="SqlAuthenticationProviders" type="System.Data.SqlClient.SqlAuthenticationProviderConfigurationSection, System.Data, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		<sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
			<section name="RoofSnap.WebAPI.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
		</sectionGroup>
	</configSections>
	<SqlAuthenticationProviders>
		<providers>
			<add name="Active Directory Interactive" type="Microsoft.Azure.Services.AppAuthentication.SqlAppAuthenticationProvider, Microsoft.Azure.Services.AppAuthentication" />
		</providers>
	</SqlAuthenticationProviders>
	<connectionStrings>
		<add name="DefaultConnection" connectionString="Server=tcp:roofsnap-dev.database.windows.net;Database=roofsnap-dev3;User ID=porthos;Encrypt=True;Authentication=Active Directory Interactive" providerName="System.Data.SqlClient" />
		<!--<add name="DefaultConnection" connectionString="Server=tcp:*************;Database=MetalRoofDb;User Id=sa;Password=******;" providerName="System.Data.SqlClient" />-->
		<add name="StorageConnectionString" connectionString="DefaultEndpointsProtocol=https;AccountName=roofsnaptest;AccountKey=****************************************************************************************" />
	</connectionStrings>
	<RaygunSettings apikey="******" excludeHttpStatusCodes="412,401,404,403,400,409,405" excludeErrorsFromLocal="false" />
	<appSettings>
		<add key="aspnet:UseTaskFriendlySynchronizationContext" value="true" />
		<add key="webpages:Enabled" value="false" />
		<add key="PreserveLoginUrl" value="true" />
		<add key="webpages:Version" value="*******" />
		<add key="apiKey" value="1B9C75B22A7A29A9E88D47C1B26BE" />
		<add key="sketchOrdersFromEmailAddress" value="<EMAIL>" />
		<add key="useTestEmailAddressesForCustomerMailers" value="true" />
		<add key="testEmailAddresses" value="<EMAIL>" />
		<add key="sketchOSAdminOrderSummaryEmailRecipients" value="<EMAIL>" />
		<add key="roofSnapTransactionSummaryEmailRecipient" value="<EMAIL>" />
		<add key="deceptiveDolphinUrl" value="https://deceptivedolphin-dev.azurewebsites.net/" />
		<add key="adminDashboardUrl" value="https://roofsnap-support-dashboard.azurewebsites.net" />
		<add key="blobStorageUrl" value="https://roofsnaptest.blob.core.windows.net" />
		<add key="CreateProjectOnOrder" value="true" />
		<!-- JWT Settings !-->
		<add key="JwtAudienceId" value="1af9339e-2f1f-4963-8c00-c7093471b48f" />
		<add key="JwtSymmetricSecurityKey" value="JElaQmIxNXI9V0UqQjlQMiFEdGQ8bEZrNUIyQVd8" />
		<add key="JwtIssuer" value="https://roofsnap-dev-auth.azurewebsites.net,https://dev-auth2.roofsnap.com" />
		<!--<add key="JwtIssuer" value="http://localhost:43926" />-->
		<!--              !-->
		<!-- New Auth Server !-->
		<add key="AuthDiscoveryUrl" value="https://dev-auth.roofsnap.com" />
		<!--              !-->
		<add key="sketchOrderProjectOwner" value="<EMAIL>" />
		<add key="sketchOrderOrganizationId" value="129526" />
		<add key="sketchOrderOfficeId" value="33157" />
		<add key="sketchServiceBillingSuperSecretKey" value="A90097AA-C8B3-4145-A10A-5A1F8A47FCA9" />
		<add key="NearmapBillingSuperSecretKey" value="F92B09D9-075B-46AD-93B9-215BE24219A2" />
		<add key="nearmapAuthUrl" value="https://us0.nearmap.com/api/0/checkaccess" />
		<add key="nearmapUserName" value="<EMAIL>" />
		<add key="nearmapPassword" value="87Y1TQlihdwZ74" />
		<add key="nearmapPurchasePrice" value="5.00" />
		<add key="nearmapAvailableImageryUrl" value="https://us0.nearmap.com/maps" />
		<add key="nearmapTileProviderUrl" value="https://us0.nearmap.com/maps" />
		<add key="nearmapApiKey" value="M2RkMjU2MDEtMDU0NC00OWIxLTkzNjktMGQ5MmUyNGI1Mzdk" />
		<add key="imageBaseUrl" value="https://cdn.roofsnap.com" />
		<add key="emailAssetsUrl" value="https://cdn2.roofsnap.com/emailassets/" />
		<add key="roofSnapUrl" value="https://dev-www.roofsnap.com/" />
		<add key="apiBaseUrl" value="https://dev-webapi.roofsnap.com/" />
		<add key="generateProjectDocumentBaseUrl" value="https://dev-www.roofsnap.com/" />
		<add key="imageGeneratorBaseUrl" value="https://dev-images.roofsnap.com/" />
		<add key="snapFeatureEnabled" value="true" />
		<add key="roofSnapMaxProxyServerEnabled" value="true" />
		<add key="cancelInactiveTrialSubscriptionsSuperSecretKey" value="6D757AC2-D52E-4169-A3C0-EDA9A7A33BE3" />
		<!-- Braintree Settings -->
		<add key="braintreeEnvironment" value="sandbox" />
		<add key="braintreeMerchantId" value="7j75yfgd2znct8sn" />
		<add key="braintreePublicKey" value="6fw9xhst7wv3wnvy" />
		<add key="braintreePrivateKey" value="********************************" />
		<!---->
		<add key="subscriptionGracePeriodInDays" value="3" />
		<!-- SendGrid Settings -->
		<add key="RoofSnapAdminEmailRecipients" value="<EMAIL>,<EMAIL>" />
		<add key="SendGridApiKey" value="*********************************************************************" />
		<add key="EmailMonitoringBcc" value="" />
		<!-- Google Maps Settings -->
		<add key="GoogleMapsApiKey" value="AIzaSyDKcQzJ6-mIvaaspEM3rw58CqaC__0cLfs" />
		<add key="SampleOfficeToCopyProjectsFrom" value="34822" />
		<add key="SampleOfficeToCopyMaterialsFrom" value="10" />
		<add key="SampleOfficeToCopyProjectsFromProjectLimit" value="9" />
		<add key="SampleOfficeToCopySettingsFrom" value="10" />
		<add key="SampleOfficeToCopyEstimateTemplatesFrom" value="10" />
		<!-- Organization Credits Settings -->
		<add key="creditValueForOrganizationInSneakPeak" value="1" />
		<add key="creditValueForOrganizationInTrial" value="3" />
		<!-- Free Sketch Order Balance Settings -->
		<add key="OrganizationStartUpFreeSketchOrderBalanceValue" value="1" />
		<!-- EventGrid Settings -->
		<add key="eventGridTopicKey" value="cEHw1SRrhidhWA/9BMCLxHlhjua7VdC0CjFT9hVFQoE=" />
		<add key="eventGridTopicEndpoint" value="tfdev-roofsnap-events.eastus-1.eventgrid.azure.net" />
		<add key="organizationCreditsEnabled" value="true" />
		<!-- Search -->
		<add key="sketchOrderSearchServiceName" value="tfdev-roofsnap-search-1" />
		<add key="sketchOrderSearchIndexName" value="dev-sketchorders" />
		<add key="sketchOrderSearchServiceKey" value="456E988E772035BBEA3977D72DAAEBC0" />
		<add key="projectSearchServiceName" value="tfprod-roofsnap-search-1" />
		<add key="projectSearchIndexName" value="dev-projects" />
		<add key="projectSearchServiceKey" value="A51C610556AF4DE37631ADE171A13F42" />
		<!-- Set azureProjectSearchDisabled to "true" to revert to using SQL only for project queries. -->
		<add key="azureProjectSearchDisabled" value="false" />
		<!-- Documents -->
		<add key="RoofSnapFunctionsStorage" value="DefaultEndpointsProtocol=https;AccountName=tfdevroofsnapfunctions;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
		<add key="DocumentsQueueName" value="create-document-requests" />
		<add key="ImageGeneratorQueueName" value="image-generator" />
		<add key="OrganizationStorageConnectionString" value="DefaultEndpointsProtocol=https;AccountName=tfdevdocumentstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
		<add key="ImageProxyUrl" value="https://roofsnap-dev-imageproxy.azurewebsites.net/" />
		<add key="DocumentSasUrlDurationDays" value="7" />
		<add key="SketchOSEmailSasUrlDurationDays" value="30" />
		<add key="EnableV2IOSDocuments" value="true" />
		<add key="CreateSupportTicketUrl" value="https://support.roofsnap.com/hc/en-us/requests/new" />
		<!-- Sales Process-->
		<add key="UseSalesforce" value="false" />
		<add key="UseNewSalesProcess" value="true" />
		<add key="UseNewSignupFlow" value="true" />
		<add key="UseNewSalesFunnel" value="false" />
		<add key="SneakPeakDurationInDays" value="14" />
		<!-- Billing Receipts -->
		<add key="BillingReceiptCompanyName" value="RoofSnap, LLC" />
		<add key="BillingReceiptCompanyAddress" value="3601 Walnut St Suite 400" />
		<add key="BillingReceiptCompanyCity" value="Denver" />
		<add key="BillingReceiptCompanyRegion" value="CO" />
		<add key="BillingReceiptCompanyPostalCode" value="80205" />
		<add key="BillingReceiptCompanyContactPhone" value="************" />
		<add key="BillingReceiptCompanyContactPhoneFormatted" value="(*************" />
		<add key="BillingReceiptCompanyContactEmail" value="<EMAIL>" />
		<add key="BillingReceiptCancellationUrl" value="https://dev-www.roofsnap.com/Account/Cancellation" />
		<add key="BillingReceiptCompanyWebsiteUrl" value="https://roofsnap.com" />
		<add key="BillingReceiptCompanyLogoUrl" value="https://cdn2.roofsnap.com/emailassets/RoofSnapLogo2TonedSmallNew.svg" />
		<add key="UpdatePaymentMethodUrl" value="https://dev-roofsnap.com/settings/billing" />
		<add key="UseReceipts" value="true" />
		<add key="SendReceiptsToCustomers" value="true" />
		<!--temporary flag to send <NAME_EMAIL> instead of real customers-->
		<add key="BillingFailedFromEmail" value="<EMAIL>" />
		<!--Salesforce-->
		<add key="SalesforceUsername" value="zap" />
		<!--these are in passbolt-->
		<add key="SalesforcePassword" value="zap" />
		<add key="SalesforceSecurityToken" value="zap" />
		<add key="SynchronizeSalesforce" value="false" />
		<!--keep this off in dev-->
		<add key="SalesforceEvercommerceUserId" value="0053h000000uZgW" />
		<!--Nearmap billing-->
		<add key="NearmapFromEmail" value="<EMAIL>" />
		<add key="NearmapFromName" value="RoofSnap Support" />
		<add key="BillingFrequencyInDays" value="7" />
		<add key="NearmapPrice" value="5.00" />
		<!--BlueRoof-->
		<add key="ProcessBlueRoofDocuments" value="false" />
		<add key="BlueRoofOrgId" value="198496" />
		<add key="BlueRoofStorageConnectionString" value="DefaultEndpointsProtocol=https;AccountName=blueroof;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;" />
		<add key="BlueRoofDocumentsContainerName" value="documents" />

		<add key="RoofSnapMaxLoggingEnabled" value="true" />

		<add key="GeoCodeApiBaseAddress" value="https://dev-geocode.roofsnap.com/api/v1/GeoCode/" />
		<add key="RoofSnapImageryBaseAddress" value="https://dev-imagery.roofsnap.com/api/v1/StaticImages/" />
		<add key="UseVexcelImagery" value="false" />
		<add key="OrgUploadsStorageConnectionString" value="DefaultEndpointsProtocol=https;AccountName=tfdevorgstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;" />

		<!--Estimate Emails-->
		<add key="EstimatesEmailEnabled" value="false" />
		<add key="EstimatesEmailAddress" value="<EMAIL>" />
		<add key="PrimaryEstimateOptionName" value="TAMKO Heritage" />

		<!--Expedited sketch orders-->
		<add key="RushOrdersEnabled" value="true" />
		<add key="NonRushDeliveryCommitmentMinutes" value="240" />
		<add key="DefaultOrganizationSettings" value="7,8" />
		<add key="FeatureFlags" value="MetalRoof;Gutters" />
		<add key="RoofSnapWebAppAddress" value="https://dev-app.roofsnap.com/" />
		
		<!-- EverPro Edge -->
		<add key="EverProEdgeClientId" value="2u7tuec45qk56p4kim5lgo7aei" />
		<add key="EverProEdgeClientSecret" value="18cbu7t3005u2kr5qr5gj509rhske5akg7f0v2feg3r7503fq7he" />
		<add key="EverProEdgeSolutionId" value="6257d670-dd48-4843-81ce-077132173bc5" />
		<add key="EverProEdgeApiBaseUrl" value="https://api.staging.optimusapp.io" />
		<add key="EverProEdgeAuthUrl" value="https://optimus-staging.auth.us-east-1.amazoncognito.com/oauth2/token" />
		<add key="EverProEdgeRewardId" value="e9a09824-82e6-47e0-bf56-3697379a3133" />
		<add key="everProEdgeSignatureKey" value="F35AA095A456EDF90E4EEE83ADB27D428F6C9874F52EACEE04F89B22AFDF5DECD5BD6D343B6AD3E3EB0608077929613563FAF310A1BF0BCF7A061B551B8DC427" />
		
		<!--Chameleon-->
		<add key="ChameleonSecretKey" value="VBVAeQukV94ZoArRDbhYiF3fhetT-1ScnvG-FbVbOTWKvzzaa4EB" />

		<!--Stripe -->
		<add key="StripeSecretKey" value="sk_test_51NdbDoEt7ziQnlINlAbxyD0vEbpjFIVTWxHlnC3g5UOctvTpBxnLYrkXFEHVaS79754rtyo5ZKnKOySSCLzYz4S400xaab6yS2" />
		<add key="StripeEndpointSecret" value="whsec_E9BbnzeOqRdwOEs2f8Yqby9e2RXx3Jlg" />

		<!--EverPro AI -->
		<add key="EverProAiApiKey" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjVlN2U0MzYxLTdhMzgtNGFlZi1iY2JiLWY2YzVjYTgxMmFlNiJ9.n1C__rcYB7KHf3jQmaewp30CpRlrercS9Qh2L6cPeC8" />
		<add key="EverProAiApiBaseUrl" value="https://dev-chat.evercommerce.com/api/chat/completions" />

		<!--Snap Estimate -->
		<add key="SnapEstimateAiPrompt" value="Create a quick estimate for a roofing project for [address], which will use [material], answer in pure JSON format as an array of material items. No extra text, like description, notes etc, should be JSON string that can be directly used in code.No '\n' needed." />
		
	</appSettings>
	<!--te-
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.5.1" />
      </system.Web>
  -->
	<system.web>
		<customErrors mode="Off" />
		<!--<compilation debug="true" targetFramework="4.6" />-->
		<!--Set maximum request length to 10 MB-->
		<httpRuntime targetFramework="4.7.2" maxRequestLength="10240" />
		<compilation targetFramework="4.7.2" debug="true">
			<assemblies>
				<add assembly="netstandard, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51" />
			</assemblies>
		</compilation>
		<pages>
			<namespaces>
				<add namespace="System.Web.Helpers" />
				<add namespace="System.Web.Mvc" />
				<add namespace="System.Web.Mvc.Ajax" />
				<add namespace="System.Web.Mvc.Html" />
				<add namespace="System.Web.Routing" />
				<add namespace="System.Web.WebPages" />
			</namespaces>
		</pages>
		<httpModules>
			<add name="TelemetryCorrelationHttpModule" type="Microsoft.AspNet.TelemetryCorrelation.TelemetryCorrelationHttpModule, Microsoft.AspNet.TelemetryCorrelation" />
			<add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" />
		</httpModules>
	</system.web>
	<system.webServer>
		<httpProtocol>
			<customHeaders>
				<add name="Access-Control-Expose-Headers" value="ETag" />
			</customHeaders>
		</httpProtocol>
		<security>
			<requestFiltering>
				<!--Set maximum allowed content length to 10 MB-->
				<requestLimits maxAllowedContentLength="********" />
			</requestFiltering>
		</security>
		<handlers>
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<remove name="OPTIONSVerbHandler" />
			<remove name="TRACEVerbHandler" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
		</handlers>
		<modules>

			<!--Stackify is here for Prefix (running locally). It shouldn't hurt anything to leave it in.-->
			<remove name="StackifyHttpModule_Net40" />
			<add name="StackifyHttpModule_Net40" type="StackifyHttpTracer.StackifyHttpModule,StackifyHttpTracer, Version=1.0.0.0, Culture=neutral, PublicKeyToken=93c44ce23f2048dd" preCondition="managedHandler,runtimeVersionv4.0" />
			<remove name="TelemetryCorrelationHttpModule" />
			<add name="TelemetryCorrelationHttpModule" type="Microsoft.AspNet.TelemetryCorrelation.TelemetryCorrelationHttpModule, Microsoft.AspNet.TelemetryCorrelation" preCondition="managedHandler" />
			<remove name="ApplicationInsightsWebTracking" />
			<add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler" />

		</modules>
		<validation validateIntegratedModeConfiguration="false" />
	</system.webServer>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Ninject" publicKeyToken="c7192dc5380945e7" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.3.4.0" newVersion="3.3.4.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Azure.Search.Service" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-10.1.0.0" newVersion="10.1.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="NetTopologySuite" publicKeyToken="f580a05016ebada1" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.15.3.0" newVersion="1.15.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.Types" publicKeyToken="89845dcd8080cc91" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-14.0.0.0" newVersion="14.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<entityFramework>
		<defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework" />
		<providers>
			<provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
		</providers>
	</entityFramework>
	<system.net>
		<mailSettings>
			<smtp from="<EMAIL>">
				<network host="smtp.sendgrid.net" port="587" userName="<EMAIL>" password="d-%&amp;/-2\6:1]@pcJzF" />
			</smtp>
		</mailSettings>
	</system.net>
	<applicationSettings>
		<RoofSnap.WebAPI.Properties.Settings>
			<setting name="RoofSnap_WebAPI_com_salesforce_my_roofsnap_SforceService" serializeAs="String">
				<value>https://login.salesforce.com/services/Soap/c/48.0/0DF3h000000PBky</value>
			</setting>
		</RoofSnap.WebAPI.Properties.Settings>
	</applicationSettings>
</configuration>