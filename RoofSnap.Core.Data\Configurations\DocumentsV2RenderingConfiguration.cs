﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RoofSnap.Core.Models;

namespace RoofSnap.Core.Data.Configurations
{
    public class DocumentsV2RenderingConfiguration : IEntityTypeConfiguration<DocumentV2RenderingModel>
    {
        public void Configure(EntityTypeBuilder<DocumentV2RenderingModel> builder)
        {
            builder.ToTable("DocumentV2Renderings", "roofsnaplive");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id)
                .HasColumnName("Id")
                .HasColumnType(SqlTypes.Int)
                .UseSqlServerIdentityColumn()
                .IsRequired();

            builder.Property(x => x.ShortCode)
                .HasColumnName("ShortCode")
                .HasComputedColumnSql("([hashids].[encode1]([ShortId]))")
                .HasColumnType(SqlTypes.Nvarchar255);

            builder.Property(x => x.CreatedAt)
                .HasColumnName("__createdAt")
                .HasColumnType(SqlTypes.DateTimeOffset3)
                .HasValueGenerator<DateTimeOffsetNowValueGenerator>()
                .IsRequired();

            builder.Property(x => x.UpdatedAt)
                .HasColumnName("__updatedAt")
                .HasColumnType(SqlTypes.DateTimeOffset3)
                .HasValueGenerator<DateTimeOffsetNowValueGenerator>()
                .IsRequired();

            builder.Property(x => x.Version)
                .HasColumnName("__version")
                .HasColumnType(SqlTypes.RowVersion)
                .IsRequired()
                .IsRowVersion()
                .HasMaxLength(8);

            builder.Property<bool>("__deleted")
                .HasDefaultValueSql("0")
                .IsRequired();
            builder.HasQueryFilter(x => !EF.Property<bool>(x, "__deleted"));

            builder.Property(x => x.DocumentId)
                .HasColumnName("DocumentId")
                .HasColumnType(SqlTypes.Int)
                .IsRequired();

            builder.Property(x => x.OrganizationId)
                .HasColumnName("OrganizationId")
                .HasColumnType(SqlTypes.BigInt)
                .IsRequired();

            builder.Property(x => x.BlobName)
                .HasColumnName("BlobName")
                .HasColumnType(SqlTypes.Nvarchar255)
                .IsRequired(false);

            builder.Property(x => x.FileName)
                .HasColumnName("FileName")
                .HasColumnType(SqlTypes.Nvarchar255)
                .IsRequired();

            builder.Property(x => x.IsGenerated)
                .HasColumnName("IsGenerated")
                .HasColumnType(SqlTypes.Bit)
                .IsRequired();

            builder.Property(x => x.ESignLink)
                .HasColumnName("ESignLink")
                .HasColumnType(SqlTypes.Nvarchar255)
                .IsRequired(false);

            builder.HasOne(x => x.Organization)
                .WithMany(x => x.Renderings)
                .HasForeignKey(x => x.OrganizationId);

            builder.HasOne(x => x.Document)
                .WithMany(x => x.Renderings)
                .HasForeignKey(x => x.DocumentId);
        }
    }
}
