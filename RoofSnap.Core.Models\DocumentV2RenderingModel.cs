﻿using System;

namespace RoofSnap.Core.Models
{
    public class DocumentV2RenderingModel
    {
        public int Id { get; set; }
        public string ShortCode { get; set; }
        public int DocumentId { get; set; }
        public string BlobName { get; set; }
        public string FileName { get; set; }
        public DateTimeOffset CreatedAt { get; set; }
        public DateTimeOffset UpdatedAt { get; set; }
        public byte[] Version { get; set; }
        public long OrganizationId { get; set; }
        public bool IsGenerated { get; set; }
        public bool IsSigned { get; set; }
        public string ESignLink { get; set; }

        public OrganizationModel Organization { get; set; }
        public DocumentV2Model Document { get; set; }
    }
}
